<?php

class FileController extends BaseController {

    public function download($id) {
        Auth::requireAuth();

        $userId = Auth::id();
        $db = Database::getInstance();

        // Get image details
        $stmt = $db->prepare("
            SELECT file_path, prompt, width, height, ai_provider, created_at
            FROM generated_images
            WHERE id = ? AND user_id = ? AND status = 'completed'
        ");
        $stmt->execute([$id, $userId]);
        $image = $stmt->fetch();

        if (!$image) {
            flash('error', 'Image not found or not ready for download.');
            $this->redirect(url('gallery'));
        }

        $filePath = $image['file_path'];

        if (!file_exists($filePath)) {
            flash('error', 'Image file not found.');
            $this->redirect(url('gallery'));
        }

        // Generate filename
        $filename = $this->generateDownloadFilename($image);

        // Set headers for download
        header('Content-Type: ' . mime_content_type($filePath));
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . filesize($filePath));
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: 0');

        // Output file
        readfile($filePath);

        // Log download
        log_activity($userId, 'image_downloaded', ['image_id' => $id]);

        exit;
    }

    public function downloadBulk($batchId) {
        Auth::requireAuth();

        $userId = Auth::id();
        $db = Database::getInstance();

        // Get batch images
        $stmt = $db->prepare("
            SELECT id, file_path, prompt, width, height, ai_provider, created_at
            FROM generated_images
            WHERE batch_id = ? AND user_id = ? AND status = 'completed'
            ORDER BY created_at ASC
        ");
        $stmt->execute([$batchId, $userId]);
        $images = $stmt->fetchAll();

        if (empty($images)) {
            flash('error', 'No completed images found in this batch.');
            $this->redirect('/gallery');
        }

        // Create temporary directory for ZIP
        $tempDir = 'storage/temp';
        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        $zipPath = $tempDir . '/batch_' . $batchId . '_' . time() . '.zip';
        $zip = new ZipArchive();

        if ($zip->open($zipPath, ZipArchive::CREATE) !== TRUE) {
            flash('error', 'Failed to create download archive.');
            $this->redirect('/gallery');
        }

        // Add images to ZIP
        foreach ($images as $index => $image) {
            if (file_exists($image['file_path'])) {
                $filename = sprintf(
                    '%03d_%s_%dx%d.png',
                    $index + 1,
                    $this->sanitizeFilename(substr($image['prompt'], 0, 30)),
                    $image['width'],
                    $image['height']
                );

                $zip->addFile($image['file_path'], $filename);
            }
        }

        // Add metadata file
        $metadata = $this->generateBatchMetadata($images, $batchId);
        $zip->addFromString('batch_info.txt', $metadata);

        $zip->close();

        // Download the ZIP file
        if (file_exists($zipPath)) {
            $downloadFilename = 'ai_images_batch_' . $batchId . '_' . date('Y-m-d') . '.zip';

            header('Content-Type: application/zip');
            header('Content-Disposition: attachment; filename="' . $downloadFilename . '"');
            header('Content-Length: ' . filesize($zipPath));
            header('Cache-Control: no-cache, must-revalidate');
            header('Expires: 0');

            readfile($zipPath);

            // Clean up
            unlink($zipPath);

            log_activity($userId, 'batch_downloaded', [
                'batch_id' => $batchId,
                'image_count' => count($images)
            ]);

            exit;
        } else {
            flash('error', 'Failed to create download file.');
            $this->redirect('/gallery');
        }
    }

    public function serve($id) {
        // Serve image for viewing (not download)
        Auth::requireAuth();

        $userId = Auth::id();
        $db = Database::getInstance();

        // Get image details
        $stmt = $db->prepare("
            SELECT file_path
            FROM generated_images
            WHERE id = ? AND user_id = ? AND status = 'completed'
        ");
        $stmt->execute([$id, $userId]);
        $image = $stmt->fetch();

        if (!$image || !file_exists($image['file_path'])) {
            http_response_code(404);
            exit('Image not found');
        }

        $filePath = $image['file_path'];

        // Set headers for viewing
        header('Content-Type: ' . mime_content_type($filePath));
        header('Content-Length: ' . filesize($filePath));
        header('Cache-Control: public, max-age=3600'); // Cache for 1 hour

        // Output file
        readfile($filePath);
        exit;
    }

    public function uploadPrompts() {
        Auth::requireAuth();
        $this->validateCsrf();

        if (!isset($_FILES['prompts_file']) || $_FILES['prompts_file']['error'] !== UPLOAD_ERR_OK) {
            flash('error', 'No file uploaded or upload error.');
            $this->redirect('/bulk-generate');
        }

        $file = $_FILES['prompts_file'];
        $allowedTypes = ['text/plain', 'text/csv', 'application/csv'];
        $maxSize = 5 * 1024 * 1024; // 5MB

        // Validate file type
        if (!in_array($file['type'], $allowedTypes)) {
            flash('error', 'Invalid file type. Please upload a TXT or CSV file.');
            $this->redirect('/bulk-generate');
        }

        // Validate file size
        if ($file['size'] > $maxSize) {
            flash('error', 'File too large. Maximum size is 5MB.');
            $this->redirect('/bulk-generate');
        }

        try {
            // Read and parse file
            $content = file_get_contents($file['tmp_name']);
            $prompts = $this->parsePromptsFile($content, $file['name']);

            if (empty($prompts)) {
                flash('error', 'No valid prompts found in the file.');
                $this->redirect('/bulk-generate');
            }

            // Store prompts in session for bulk generation form
            $_SESSION['uploaded_prompts'] = $prompts;
            $_SESSION['prompts_filename'] = $file['name'];

            flash('success', 'File uploaded successfully! Found ' . count($prompts) . ' prompts.');
            $this->redirect('/bulk-generate');

        } catch (Exception $e) {
            log_error('Prompts file upload failed: ' . $e->getMessage());
            flash('error', 'Failed to process uploaded file.');
            $this->redirect('/bulk-generate');
        }
    }

    public function deleteFile($id) {
        Auth::requireAuth();
        $this->validateCsrf();

        $userId = Auth::id();
        $db = Database::getInstance();

        try {
            // Get image details
            $stmt = $db->prepare("
                SELECT file_path
                FROM generated_images
                WHERE id = ? AND user_id = ?
            ");
            $stmt->execute([$id, $userId]);
            $image = $stmt->fetch();

            if (!$image) {
                $this->json(['success' => false, 'error' => 'Image not found.'], 404);
            }

            // Delete file if it exists
            if (file_exists($image['file_path'])) {
                unlink($image['file_path']);
            }

            // Delete database record
            $stmt = $db->prepare("DELETE FROM generated_images WHERE id = ? AND user_id = ?");
            $stmt->execute([$id, $userId]);

            log_activity($userId, 'image_deleted', ['image_id' => $id]);

            $this->json(['success' => true, 'message' => 'Image deleted successfully.']);

        } catch (Exception $e) {
            log_error('Image deletion failed: ' . $e->getMessage(), ['image_id' => $id]);
            $this->json(['success' => false, 'error' => 'Failed to delete image.'], 500);
        }
    }

    public function getUploadProgress($uploadId) {
        // For tracking upload progress (if implemented with AJAX uploads)
        Auth::requireAuth();

        $progress = $_SESSION['upload_progress_' . $uploadId] ?? null;

        if ($progress) {
            $this->json([
                'success' => true,
                'progress' => $progress
            ]);
        } else {
            $this->json([
                'success' => false,
                'error' => 'Upload not found.'
            ], 404);
        }
    }

    private function generateDownloadFilename($image) {
        $timestamp = date('Y-m-d_H-i-s', strtotime($image['created_at']));
        $promptSnippet = $this->sanitizeFilename(substr($image['prompt'], 0, 30));
        $dimensions = $image['width'] . 'x' . $image['height'];
        $provider = $image['ai_provider'];

        return "ai_image_{$timestamp}_{$promptSnippet}_{$dimensions}_{$provider}.png";
    }

    private function sanitizeFilename($filename) {
        // Remove or replace invalid filename characters
        $filename = preg_replace('/[^a-zA-Z0-9\-_\.]/', '_', $filename);
        $filename = preg_replace('/_{2,}/', '_', $filename); // Replace multiple underscores
        return trim($filename, '_');
    }

    private function parsePromptsFile($content, $filename) {
        $prompts = [];
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

        if ($extension === 'csv') {
            // Parse CSV file
            $lines = str_getcsv($content, "\n");
            foreach ($lines as $line) {
                $data = str_getcsv($line);
                if (!empty($data[0])) {
                    $prompt = trim($data[0]);
                    if (strlen($prompt) >= 3) {
                        $prompts[] = $prompt;
                    }
                }
            }
        } else {
            // Parse TXT file (one prompt per line)
            $lines = explode("\n", $content);
            foreach ($lines as $line) {
                $prompt = trim($line);
                if (strlen($prompt) >= 3) {
                    $prompts[] = $prompt;
                }
            }
        }

        // Remove duplicates and limit
        $prompts = array_unique($prompts);
        return array_slice($prompts, 0, 100); // Limit to 100 prompts
    }

    private function generateBatchMetadata($images, $batchId) {
        $metadata = "AI Image Generation Batch Report\n";
        $metadata .= "=====================================\n\n";
        $metadata .= "Batch ID: {$batchId}\n";
        $metadata .= "Generated: " . date('Y-m-d H:i:s') . "\n";
        $metadata .= "Total Images: " . count($images) . "\n\n";

        $metadata .= "Image Details:\n";
        $metadata .= "--------------\n";

        foreach ($images as $index => $image) {
            $metadata .= sprintf(
                "%03d. %s\n     Size: %dx%d | Provider: %s | Created: %s\n\n",
                $index + 1,
                $image['prompt'],
                $image['width'],
                $image['height'],
                $image['ai_provider'],
                date('Y-m-d H:i:s', strtotime($image['created_at']))
            );
        }

        return $metadata;
    }

    public function cleanupOldFiles() {
        // Admin function to clean up old files
        Auth::requireAdmin();

        $db = Database::getInstance();
        $cutoffDate = date('Y-m-d H:i:s', strtotime('-30 days'));

        try {
            // Get old files
            $stmt = $db->prepare("
                SELECT file_path
                FROM generated_images
                WHERE created_at < ? AND status = 'completed'
            ");
            $stmt->execute([$cutoffDate]);
            $oldFiles = $stmt->fetchAll(PDO::FETCH_COLUMN);

            $deletedCount = 0;
            foreach ($oldFiles as $filePath) {
                if (file_exists($filePath)) {
                    unlink($filePath);
                    $deletedCount++;
                }
            }

            // Update database records
            $stmt = $db->prepare("
                UPDATE generated_images
                SET file_path = NULL
                WHERE created_at < ? AND status = 'completed'
            ");
            $stmt->execute([$cutoffDate]);

            log_activity(Auth::id(), 'admin_cleanup_files', ['deleted_count' => $deletedCount]);

            $this->json([
                'success' => true,
                'message' => "Cleaned up {$deletedCount} old files."
            ]);

        } catch (Exception $e) {
            log_error('File cleanup failed: ' . $e->getMessage());
            $this->json([
                'success' => false,
                'error' => 'Failed to cleanup files.'
            ], 500);
        }
    }
}
