<?php
ob_start();
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 fw-bold">Analytics Dashboard</h1>
                    <p class="text-muted">Detailed insights and analytics</p>
                </div>
                <div>
                    <a href="<?= url('admin') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- User Analytics -->
    <div class="row mb-4">
        <div class="col">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">User Registrations (Last 30 Days)</h5>
                </div>
                <div class="card-body">
                    <canvas id="userRegistrationsChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <!-- Revenue Analytics -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Revenue by Plan</h5>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Provider Usage -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">AI Provider Usage</h5>
                </div>
                <div class="card-body">
                    <canvas id="providerChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Detailed Tables -->
    <div class="row">
        <!-- Revenue Breakdown -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Revenue Breakdown</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($analytics['revenue'])): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Plan</th>
                                        <th>Subscribers</th>
                                        <th>Revenue</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($analytics['revenue'] as $plan): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($plan['name']) ?></td>
                                            <td><?= number_format($plan['subscribers']) ?></td>
                                            <td>$<?= number_format($plan['revenue'], 2) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">No revenue data available</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Provider Statistics -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Provider Statistics</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($analytics['providers'])): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Provider</th>
                                        <th>Users</th>
                                        <th>Usage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($analytics['providers'] as $provider): ?>
                                        <tr>
                                            <td><?= ucfirst(str_replace('_', ' ', $provider['provider'])) ?></td>
                                            <td><?= number_format($provider['users_count']) ?></td>
                                            <td>
                                                <?php
                                                $usage = 0;
                                                foreach ($analytics['usage'] as $usageData) {
                                                    if ($usageData['ai_provider'] === $provider['provider']) {
                                                        $usage = $usageData['count'];
                                                        break;
                                                    }
                                                }
                                                echo number_format($usage);
                                                ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">No provider data available</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // User Registrations Chart
    const userCtx = document.getElementById('userRegistrationsChart').getContext('2d');
    const userRegistrationsData = <?= json_encode($analytics['users']) ?>;
    
    new Chart(userCtx, {
        type: 'line',
        data: {
            labels: userRegistrationsData.map(item => item.date),
            datasets: [{
                label: 'New Users',
                data: userRegistrationsData.map(item => item.count),
                borderColor: 'rgb(99, 102, 241)',
                backgroundColor: 'rgba(99, 102, 241, 0.1)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    const revenueData = <?= json_encode($analytics['revenue']) ?>;
    
    new Chart(revenueCtx, {
        type: 'doughnut',
        data: {
            labels: revenueData.map(item => item.name),
            datasets: [{
                data: revenueData.map(item => item.revenue),
                backgroundColor: [
                    'rgb(99, 102, 241)',
                    'rgb(16, 185, 129)',
                    'rgb(245, 158, 11)',
                    'rgb(239, 68, 68)',
                    'rgb(139, 92, 246)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // Provider Usage Chart
    const providerCtx = document.getElementById('providerChart').getContext('2d');
    const usageData = <?= json_encode($analytics['usage']) ?>;
    
    new Chart(providerCtx, {
        type: 'bar',
        data: {
            labels: usageData.map(item => item.ai_provider.replace('_', ' ')),
            datasets: [{
                label: 'Images Generated',
                data: usageData.map(item => item.count),
                backgroundColor: 'rgba(99, 102, 241, 0.8)',
                borderColor: 'rgb(99, 102, 241)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
});
</script>

<?php
$content = ob_get_clean();
$title = 'Analytics';
include __DIR__ . '/../layout/app.php';
?>
