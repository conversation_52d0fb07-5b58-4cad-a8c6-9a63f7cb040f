<?php

class GalleryController extends BaseController {
    
    public function index() {
        Auth::requireAuth();
        
        $userId = Auth::id();
        $db = Database::getInstance();
        
        // Get filters
        $status = sanitize($_GET['status'] ?? '');
        $provider = sanitize($_GET['provider'] ?? '');
        $batchId = sanitize($_GET['batch'] ?? '');
        $page = max(1, (int)($_GET['page'] ?? 1));
        $perPage = 12;
        $offset = ($page - 1) * $perPage;
        
        // Build query
        $whereConditions = ['user_id = ?'];
        $params = [$userId];
        
        if (!empty($status)) {
            $whereConditions[] = 'status = ?';
            $params[] = $status;
        }
        
        if (!empty($provider)) {
            $whereConditions[] = 'ai_provider = ?';
            $params[] = $provider;
        }
        
        if (!empty($batchId)) {
            $whereConditions[] = 'batch_id = ?';
            $params[] = $batchId;
        }
        
        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        
        // Get total count
        $countQuery = "SELECT COUNT(*) FROM generated_images $whereClause";
        $stmt = $db->prepare($countQuery);
        $stmt->execute($params);
        $totalImages = $stmt->fetchColumn();
        
        // Get images
        $query = "
            SELECT * FROM generated_images 
            $whereClause 
            ORDER BY created_at DESC 
            LIMIT $perPage OFFSET $offset
        ";
        $stmt = $db->prepare($query);
        $stmt->execute($params);
        $images = $stmt->fetchAll();
        
        // Get filter options
        $stmt = $db->prepare("SELECT DISTINCT ai_provider FROM generated_images WHERE user_id = ? ORDER BY ai_provider");
        $stmt->execute([$userId]);
        $providers = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Get batch info if viewing specific batch
        $batchInfo = null;
        if (!empty($batchId)) {
            $stmt = $db->prepare("
                SELECT 
                    COUNT(*) as total_images,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_images,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_images,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_images,
                    SUM(CASE WHEN status = 'generating' THEN 1 ELSE 0 END) as generating_images
                FROM generated_images 
                WHERE user_id = ? AND batch_id = ?
            ");
            $stmt->execute([$userId, $batchId]);
            $batchInfo = $stmt->fetch();
            $batchInfo['batch_id'] = $batchId;
        }
        
        // Calculate pagination
        $totalPages = ceil($totalImages / $perPage);
        
        $this->view('gallery/index', [
            'images' => $images,
            'providers' => $providers,
            'filters' => [
                'status' => $status,
                'provider' => $provider,
                'batch' => $batchId
            ],
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_images' => $totalImages,
                'per_page' => $perPage
            ],
            'batchInfo' => $batchInfo
        ]);
    }
    
    public function show($id) {
        Auth::requireAuth();
        
        $userId = Auth::id();
        $db = Database::getInstance();
        
        // Get image details
        $stmt = $db->prepare("SELECT * FROM generated_images WHERE id = ? AND user_id = ?");
        $stmt->execute([$id, $userId]);
        $image = $stmt->fetch();
        
        if (!$image) {
            flash('error', 'Image not found.');
            $this->redirect('/gallery');
        }
        
        // Get related images (same batch or similar)
        $relatedImages = [];
        if ($image['batch_id']) {
            $stmt = $db->prepare("
                SELECT * FROM generated_images 
                WHERE batch_id = ? AND id != ? AND user_id = ? 
                ORDER BY created_at DESC 
                LIMIT 6
            ");
            $stmt->execute([$image['batch_id'], $id, $userId]);
            $relatedImages = $stmt->fetchAll();
        } else {
            $stmt = $db->prepare("
                SELECT * FROM generated_images 
                WHERE ai_provider = ? AND id != ? AND user_id = ? 
                ORDER BY created_at DESC 
                LIMIT 6
            ");
            $stmt->execute([$image['ai_provider'], $id, $userId]);
            $relatedImages = $stmt->fetchAll();
        }
        
        $this->view('gallery/show', [
            'image' => $image,
            'relatedImages' => $relatedImages
        ]);
    }
    
    public function delete($id) {
        Auth::requireAuth();
        $this->validateCsrf();
        
        $userId = Auth::id();
        $db = Database::getInstance();
        
        try {
            // Get image details
            $stmt = $db->prepare("SELECT file_path FROM generated_images WHERE id = ? AND user_id = ?");
            $stmt->execute([$id, $userId]);
            $image = $stmt->fetch();
            
            if (!$image) {
                flash('error', 'Image not found.');
                $this->redirect('/gallery');
            }
            
            // Delete file if it exists
            if (file_exists($image['file_path'])) {
                unlink($image['file_path']);
            }
            
            // Delete database record
            $stmt = $db->prepare("DELETE FROM generated_images WHERE id = ? AND user_id = ?");
            $stmt->execute([$id, $userId]);
            
            flash('success', 'Image deleted successfully.');
            log_activity($userId, 'image_deleted', ['image_id' => $id]);
            
        } catch (Exception $e) {
            log_error('Image deletion failed: ' . $e->getMessage(), ['image_id' => $id]);
            flash('error', 'Failed to delete image. Please try again.');
        }
        
        $this->redirect('/gallery');
    }
    
    public function bulkDelete() {
        Auth::requireAuth();
        $this->validateCsrf();
        
        $userId = Auth::id();
        $imageIds = $_POST['image_ids'] ?? [];
        
        if (empty($imageIds) || !is_array($imageIds)) {
            flash('error', 'No images selected.');
            $this->redirect('/gallery');
        }
        
        try {
            $db = Database::getInstance();
            $db->beginTransaction();
            
            $placeholders = str_repeat('?,', count($imageIds) - 1) . '?';
            $params = array_merge($imageIds, [$userId]);
            
            // Get file paths
            $stmt = $db->prepare("
                SELECT file_path FROM generated_images 
                WHERE id IN ($placeholders) AND user_id = ?
            ");
            $stmt->execute($params);
            $filePaths = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            // Delete files
            foreach ($filePaths as $filePath) {
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }
            
            // Delete database records
            $stmt = $db->prepare("
                DELETE FROM generated_images 
                WHERE id IN ($placeholders) AND user_id = ?
            ");
            $stmt->execute($params);
            
            $deletedCount = $stmt->rowCount();
            $db->commit();
            
            flash('success', "Successfully deleted $deletedCount images.");
            log_activity($userId, 'bulk_images_deleted', ['count' => $deletedCount]);
            
        } catch (Exception $e) {
            $db->rollback();
            log_error('Bulk image deletion failed: ' . $e->getMessage(), ['image_ids' => $imageIds]);
            flash('error', 'Failed to delete images. Please try again.');
        }
        
        $this->redirect('/gallery');
    }
    
    public function downloadBatch($batchId) {
        Auth::requireAuth();
        
        $userId = Auth::id();
        $db = Database::getInstance();
        
        // Get batch images
        $stmt = $db->prepare("
            SELECT file_path, prompt FROM generated_images 
            WHERE batch_id = ? AND user_id = ? AND status = 'completed'
        ");
        $stmt->execute([$batchId, $userId]);
        $images = $stmt->fetchAll();
        
        if (empty($images)) {
            flash('error', 'No completed images found in this batch.');
            $this->redirect('/gallery');
        }
        
        // Create ZIP file
        $zipPath = 'storage/temp/batch_' . $batchId . '_' . time() . '.zip';
        $zip = new ZipArchive();
        
        if ($zip->open($zipPath, ZipArchive::CREATE) !== TRUE) {
            flash('error', 'Failed to create download archive.');
            $this->redirect('/gallery');
        }
        
        foreach ($images as $index => $image) {
            if (file_exists($image['file_path'])) {
                $filename = sprintf('%03d_%s.png', $index + 1, substr(md5($image['prompt']), 0, 8));
                $zip->addFile($image['file_path'], $filename);
            }
        }
        
        $zip->close();
        
        // Download the ZIP file
        if (file_exists($zipPath)) {
            header('Content-Type: application/zip');
            header('Content-Disposition: attachment; filename="batch_' . $batchId . '.zip"');
            header('Content-Length: ' . filesize($zipPath));
            readfile($zipPath);
            
            // Clean up
            unlink($zipPath);
            
            log_activity($userId, 'batch_downloaded', ['batch_id' => $batchId, 'image_count' => count($images)]);
            exit;
        } else {
            flash('error', 'Failed to create download file.');
            $this->redirect('/gallery');
        }
    }
    
    public function regenerate($id) {
        Auth::requireAuth();
        $this->validateCsrf();
        
        $userId = Auth::id();
        
        // Check if user can generate images
        if (!Auth::canGenerateImage($userId)) {
            flash('error', 'You have reached your generation limit for today.');
            $this->redirect('/gallery');
        }
        
        $db = Database::getInstance();
        
        try {
            // Get original image details
            $stmt = $db->prepare("SELECT * FROM generated_images WHERE id = ? AND user_id = ?");
            $stmt->execute([$id, $userId]);
            $originalImage = $stmt->fetch();
            
            if (!$originalImage) {
                flash('error', 'Original image not found.');
                $this->redirect('/gallery');
            }
            
            // Check if user has API key for the provider
            $stmt = $db->prepare("SELECT api_key FROM user_ai_provider_keys WHERE user_id = ? AND provider = ? AND is_active = 1");
            $stmt->execute([$userId, $originalImage['ai_provider']]);
            $apiKeyData = $stmt->fetch();
            
            if (!$apiKeyData) {
                flash('error', 'No API key configured for this provider.');
                $this->redirect('/gallery');
            }
            
            // Create new image generation record
            $filename = 'regen_' . time() . '_' . uniqid() . '.png';
            $filePath = 'storage/generated/' . $filename;
            
            $stmt = $db->prepare("
                INSERT INTO generated_images (user_id, prompt, negative_prompt, width, height, style, ai_provider, file_path, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending')
            ");
            
            $stmt->execute([
                $userId,
                $originalImage['prompt'],
                $originalImage['negative_prompt'],
                $originalImage['width'],
                $originalImage['height'],
                $originalImage['style'],
                $originalImage['ai_provider'],
                $filePath
            ]);
            
            $newImageId = $db->lastInsertId();
            
            // Record usage
            Auth::recordUsage($userId, 'image_regeneration');
            
            flash('success', 'Image regeneration started!');
            $this->redirect('/gallery/' . $newImageId);
            
        } catch (Exception $e) {
            log_error('Image regeneration failed: ' . $e->getMessage(), ['original_id' => $id]);
            flash('error', 'Failed to regenerate image. Please try again.');
            $this->redirect('/gallery');
        }
    }
}
