<?php
ob_start();
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 fw-bold">System Settings</h1>
                    <p class="text-muted">Configure global application settings</p>
                </div>
                <div>
                    <a href="<?= url('admin') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <form method="POST" action="<?= url('admin/settings') ?>">
        <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
        
        <div class="row">
            <!-- General Settings -->
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0 py-3">
                        <h5 class="mb-0">General Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="site_name" class="form-label">Site Name</label>
                            <input type="text" class="form-control" id="site_name" name="site_name" 
                                   value="<?= htmlspecialchars($settings['site_name'] ?? '') ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="site_description" class="form-label">Site Description</label>
                            <textarea class="form-control" id="site_description" name="site_description" rows="3"><?= htmlspecialchars($settings['site_description'] ?? '') ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="site_logo" class="form-label">Site Logo URL</label>
                            <input type="url" class="form-control" id="site_logo" name="site_logo" 
                                   value="<?= htmlspecialchars($settings['site_logo'] ?? '') ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="site_favicon" class="form-label">Favicon URL</label>
                            <input type="url" class="form-control" id="site_favicon" name="site_favicon" 
                                   value="<?= htmlspecialchars($settings['site_favicon'] ?? '') ?>">
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode" 
                                   value="true" <?= ($settings['maintenance_mode'] ?? 'false') === 'true' ? 'checked' : '' ?>>
                            <label class="form-check-label" for="maintenance_mode">
                                Maintenance Mode
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- User Settings -->
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0 py-3">
                        <h5 class="mb-0">User Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="registration_enabled" name="registration_enabled" 
                                   value="true" <?= ($settings['registration_enabled'] ?? 'true') === 'true' ? 'checked' : '' ?>>
                            <label class="form-check-label" for="registration_enabled">
                                Allow User Registration
                            </label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="email_verification_required" name="email_verification_required" 
                                   value="true" <?= ($settings['email_verification_required'] ?? 'false') === 'true' ? 'checked' : '' ?>>
                            <label class="form-check-label" for="email_verification_required">
                                Require Email Verification
                            </label>
                        </div>
                        
                        <div class="mb-3">
                            <label for="default_user_plan" class="form-label">Default User Plan</label>
                            <select class="form-select" id="default_user_plan" name="default_user_plan">
                                <option value="1" <?= ($settings['default_user_plan'] ?? '1') === '1' ? 'selected' : '' ?>>Free</option>
                                <option value="2" <?= ($settings['default_user_plan'] ?? '1') === '2' ? 'selected' : '' ?>>Pro</option>
                                <option value="3" <?= ($settings['default_user_plan'] ?? '1') === '3' ? 'selected' : '' ?>>Business</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="max_prompt_length" class="form-label">Max Prompt Length</label>
                            <input type="number" class="form-control" id="max_prompt_length" name="max_prompt_length" 
                                   value="<?= htmlspecialchars($settings['max_prompt_length'] ?? '1000') ?>" min="100" max="5000">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- AI Provider Settings -->
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0 py-3">
                        <h5 class="mb-0">AI Provider Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="default_ai_provider" class="form-label">Default AI Provider</label>
                            <select class="form-select" id="default_ai_provider" name="default_ai_provider">
                                <option value="together_ai" <?= ($settings['default_ai_provider'] ?? 'together_ai') === 'together_ai' ? 'selected' : '' ?>>Together AI</option>
                                <option value="runware" <?= ($settings['default_ai_provider'] ?? 'together_ai') === 'runware' ? 'selected' : '' ?>>Runware</option>
                                <option value="replicate" <?= ($settings['default_ai_provider'] ?? 'together_ai') === 'replicate' ? 'selected' : '' ?>>Replicate</option>
                                <option value="fal" <?= ($settings['default_ai_provider'] ?? 'together_ai') === 'fal' ? 'selected' : '' ?>>FAL AI</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="max_image_size" class="form-label">Max Image Size (MB)</label>
                            <input type="number" class="form-control" id="max_image_size" name="max_image_size" 
                                   value="<?= htmlspecialchars($settings['max_image_size'] ?? '10') ?>" min="1" max="50">
                        </div>
                        
                        <div class="mb-3">
                            <label for="generation_timeout" class="form-label">Generation Timeout (seconds)</label>
                            <input type="number" class="form-control" id="generation_timeout" name="generation_timeout" 
                                   value="<?= htmlspecialchars($settings['generation_timeout'] ?? '300') ?>" min="30" max="600">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Email Settings -->
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0 py-3">
                        <h5 class="mb-0">Email Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="smtp_host" class="form-label">SMTP Host</label>
                            <input type="text" class="form-control" id="smtp_host" name="smtp_host" 
                                   value="<?= htmlspecialchars($settings['smtp_host'] ?? '') ?>">
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="smtp_port" class="form-label">SMTP Port</label>
                                <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                       value="<?= htmlspecialchars($settings['smtp_port'] ?? '587') ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="smtp_encryption" class="form-label">Encryption</label>
                                <select class="form-select" id="smtp_encryption" name="smtp_encryption">
                                    <option value="tls" <?= ($settings['smtp_encryption'] ?? 'tls') === 'tls' ? 'selected' : '' ?>>TLS</option>
                                    <option value="ssl" <?= ($settings['smtp_encryption'] ?? 'tls') === 'ssl' ? 'selected' : '' ?>>SSL</option>
                                    <option value="" <?= ($settings['smtp_encryption'] ?? 'tls') === '' ? 'selected' : '' ?>>None</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="smtp_username" class="form-label">SMTP Username</label>
                            <input type="text" class="form-control" id="smtp_username" name="smtp_username" 
                                   value="<?= htmlspecialchars($settings['smtp_username'] ?? '') ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="smtp_password" class="form-label">SMTP Password</label>
                            <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                                   value="<?= htmlspecialchars($settings['smtp_password'] ?? '') ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="mail_from_address" class="form-label">From Email Address</label>
                            <input type="email" class="form-control" id="mail_from_address" name="mail_from_address" 
                                   value="<?= htmlspecialchars($settings['mail_from_address'] ?? '') ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="mail_from_name" class="form-label">From Name</label>
                            <input type="text" class="form-control" id="mail_from_name" name="mail_from_name" 
                                   value="<?= htmlspecialchars($settings['mail_from_name'] ?? '') ?>">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Security Settings -->
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0 py-3">
                        <h5 class="mb-0">Security Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="session_lifetime" class="form-label">Session Lifetime (minutes)</label>
                            <input type="number" class="form-control" id="session_lifetime" name="session_lifetime" 
                                   value="<?= htmlspecialchars($settings['session_lifetime'] ?? '120') ?>" min="15" max="1440">
                        </div>
                        
                        <div class="mb-3">
                            <label for="password_reset_expiry" class="form-label">Password Reset Expiry (hours)</label>
                            <input type="number" class="form-control" id="password_reset_expiry" name="password_reset_expiry" 
                                   value="<?= htmlspecialchars($settings['password_reset_expiry'] ?? '24') ?>" min="1" max="72">
                        </div>
                        
                        <div class="mb-3">
                            <label for="max_login_attempts" class="form-label">Max Login Attempts</label>
                            <input type="number" class="form-control" id="max_login_attempts" name="max_login_attempts" 
                                   value="<?= htmlspecialchars($settings['max_login_attempts'] ?? '5') ?>" min="3" max="10">
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="force_https" name="force_https" 
                                   value="true" <?= ($settings['force_https'] ?? 'false') === 'true' ? 'checked' : '' ?>>
                            <label class="form-check-label" for="force_https">
                                Force HTTPS
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Analytics Settings -->
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0 py-3">
                        <h5 class="mb-0">Analytics & Tracking</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="google_analytics_id" class="form-label">Google Analytics ID</label>
                            <input type="text" class="form-control" id="google_analytics_id" name="google_analytics_id" 
                                   value="<?= htmlspecialchars($settings['google_analytics_id'] ?? '') ?>" 
                                   placeholder="G-XXXXXXXXXX">
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="enable_user_tracking" name="enable_user_tracking" 
                                   value="true" <?= ($settings['enable_user_tracking'] ?? 'true') === 'true' ? 'checked' : '' ?>>
                            <label class="form-check-label" for="enable_user_tracking">
                                Enable User Activity Tracking
                            </label>
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enable_error_reporting" name="enable_error_reporting" 
                                   value="true" <?= ($settings['enable_error_reporting'] ?? 'true') === 'true' ? 'checked' : '' ?>>
                            <label class="form-check-label" for="enable_error_reporting">
                                Enable Error Reporting
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Save Button -->
        <div class="row">
            <div class="col">
                <div class="d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-2"></i>Save Settings
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<?php
$content = ob_get_clean();
$title = 'System Settings';
include __DIR__ . '/../layout/app.php';
?>
