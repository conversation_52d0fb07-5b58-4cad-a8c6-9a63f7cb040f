<?php
ob_start();
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 fw-bold">Image Gallery</h1>
            <p class="text-muted">View and manage your generated images</p>
        </div>
    </div>
    
    <!-- Batch Info (if viewing specific batch) -->
    <?php if ($batchInfo): ?>
        <div class="row mb-4">
            <div class="col">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-layer-group me-2"></i>Batch: <?= htmlspecialchars($batchInfo['batch_id']) ?>
                        </h5>
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h4 class="text-primary"><?= $batchInfo['total_images'] ?></h4>
                                    <small class="text-muted">Total Images</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h4 class="text-success"><?= $batchInfo['completed_images'] ?></h4>
                                    <small class="text-muted">Completed</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h4 class="text-warning"><?= $batchInfo['pending_images'] + $batchInfo['generating_images'] ?></h4>
                                    <small class="text-muted">Processing</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h4 class="text-danger"><?= $batchInfo['failed_images'] ?></h4>
                                    <small class="text-muted">Failed</small>
                                </div>
                            </div>
                        </div>
                        <?php if ($batchInfo['completed_images'] > 0): ?>
                            <div class="mt-3">
                                <a href="/download/bulk/<?= htmlspecialchars($batchInfo['batch_id']) ?>" class="btn btn-primary">
                                    <i class="fas fa-download me-2"></i>Download All Completed
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Filters -->
    <div class="row mb-4">
        <div class="col">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" action="/gallery" class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="completed" <?= $filters['status'] === 'completed' ? 'selected' : '' ?>>Completed</option>
                                <option value="pending" <?= $filters['status'] === 'pending' ? 'selected' : '' ?>>Pending</option>
                                <option value="generating" <?= $filters['status'] === 'generating' ? 'selected' : '' ?>>Generating</option>
                                <option value="failed" <?= $filters['status'] === 'failed' ? 'selected' : '' ?>>Failed</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="provider" class="form-label">AI Provider</label>
                            <select class="form-select" id="provider" name="provider">
                                <option value="">All Providers</option>
                                <?php foreach ($providers as $provider): ?>
                                    <option value="<?= htmlspecialchars($provider) ?>" <?= $filters['provider'] === $provider ? 'selected' : '' ?>>
                                        <?= ucfirst(str_replace('_', ' ', $provider)) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-filter me-1"></i>Filter
                            </button>
                            <a href="/gallery" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Clear
                            </a>
                        </div>
                        
                        <div class="col-md-3 d-flex align-items-end justify-content-end">
                            <button type="button" class="btn btn-outline-danger" id="bulk-delete-btn" style="display: none;">
                                <i class="fas fa-trash me-1"></i>Delete Selected
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Images Grid -->
    <?php if (empty($images)): ?>
        <div class="row">
            <div class="col">
                <div class="text-center py-5">
                    <i class="fas fa-images fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">No Images Found</h4>
                    <p class="text-muted">
                        <?php if (!empty($filters['status']) || !empty($filters['provider'])): ?>
                            No images match your current filters.
                        <?php else: ?>
                            You haven't generated any images yet.
                        <?php endif; ?>
                    </p>
                    <a href="/generate" class="btn btn-primary">
                        <i class="fas fa-magic me-2"></i>Generate Your First Image
                    </a>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="row g-4">
            <?php foreach ($images as $image): ?>
                <div class="col-xl-3 col-lg-4 col-md-6">
                    <div class="card border-0 shadow-sm image-card">
                        <div class="position-relative">
                            <?php if ($image['status'] === 'completed'): ?>
                                <img src="<?= asset($image['file_path']) ?>" 
                                     class="card-img-top" 
                                     alt="Generated image"
                                     style="height: 200px; object-fit: cover; cursor: pointer;"
                                     onclick="viewImage(<?= $image['id'] ?>)">
                            <?php else: ?>
                                <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 200px;">
                                    <?php if ($image['status'] === 'pending'): ?>
                                        <div class="text-center">
                                            <i class="fas fa-clock fa-2x text-muted mb-2"></i>
                                            <div class="text-muted">Pending</div>
                                        </div>
                                    <?php elseif ($image['status'] === 'generating'): ?>
                                        <div class="text-center">
                                            <div class="spinner-border text-primary mb-2" role="status"></div>
                                            <div class="text-muted">Generating...</div>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-center">
                                            <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                                            <div class="text-danger">Failed</div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Status Badge -->
                            <div class="position-absolute top-0 end-0 m-2">
                                <?php
                                $badgeClass = [
                                    'completed' => 'bg-success',
                                    'pending' => 'bg-warning',
                                    'generating' => 'bg-info',
                                    'failed' => 'bg-danger'
                                ][$image['status']] ?? 'bg-secondary';
                                ?>
                                <span class="badge <?= $badgeClass ?>"><?= ucfirst($image['status']) ?></span>
                            </div>
                            
                            <!-- Selection Checkbox -->
                            <div class="position-absolute top-0 start-0 m-2">
                                <input type="checkbox" class="form-check-input image-select" value="<?= $image['id'] ?>">
                            </div>
                        </div>
                        
                        <div class="card-body">
                            <h6 class="card-title text-truncate" title="<?= htmlspecialchars($image['prompt']) ?>">
                                <?= htmlspecialchars(substr($image['prompt'], 0, 50)) ?><?= strlen($image['prompt']) > 50 ? '...' : '' ?>
                            </h6>
                            
                            <div class="row text-muted small mb-2">
                                <div class="col-6">
                                    <i class="fas fa-expand-arrows-alt me-1"></i>
                                    <?= $image['width'] ?>×<?= $image['height'] ?>
                                </div>
                                <div class="col-6">
                                    <i class="fas fa-brain me-1"></i>
                                    <?= ucfirst(str_replace('_', ' ', $image['ai_provider'])) ?>
                                </div>
                            </div>
                            
                            <div class="text-muted small mb-3">
                                <i class="fas fa-clock me-1"></i>
                                <?= time_ago($image['created_at']) ?>
                            </div>
                            
                            <div class="btn-group w-100" role="group">
                                <?php if ($image['status'] === 'completed'): ?>
                                    <a href="/gallery/<?= $image['id'] ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="/download/<?= $image['id'] ?>" class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="regenerateImage(<?= $image['id'] ?>)">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                <?php endif; ?>
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteImage(<?= $image['id'] ?>)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Pagination -->
        <?php if ($pagination['total_pages'] > 1): ?>
            <div class="row mt-4">
                <div class="col">
                    <nav aria-label="Gallery pagination">
                        <ul class="pagination justify-content-center">
                            <?php if ($pagination['current_page'] > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?= $pagination['current_page'] - 1 ?>&<?= http_build_query($filters) ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=<?= $i ?>&<?= http_build_query($filters) ?>"><?= $i ?></a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?= $pagination['current_page'] + 1 ?>&<?= http_build_query($filters) ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    
                    <div class="text-center text-muted">
                        Showing <?= count($images) ?> of <?= $pagination['total_images'] ?> images
                    </div>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<script>
// Image selection handling
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.image-select');
    const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
    
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });
    
    function updateBulkActions() {
        const selected = document.querySelectorAll('.image-select:checked');
        bulkDeleteBtn.style.display = selected.length > 0 ? 'block' : 'none';
    }
    
    // Bulk delete handler
    bulkDeleteBtn.addEventListener('click', function() {
        const selected = Array.from(document.querySelectorAll('.image-select:checked')).map(cb => cb.value);
        
        if (selected.length === 0) return;
        
        if (confirm(`Are you sure you want to delete ${selected.length} selected images?`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/gallery/bulk-delete';
            
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            form.appendChild(csrfInput);
            
            selected.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'image_ids[]';
                input.value = id;
                form.appendChild(input);
            });
            
            document.body.appendChild(form);
            form.submit();
        }
    });
});

function viewImage(id) {
    window.location.href = '/gallery/' + id;
}

function deleteImage(id) {
    if (confirm('Are you sure you want to delete this image?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/gallery/' + id + '/delete';
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        form.appendChild(csrfInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}

function regenerateImage(id) {
    if (confirm('Are you sure you want to regenerate this image? This will use one of your generations.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/gallery/' + id + '/regenerate';
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        form.appendChild(csrfInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php
$content = ob_get_clean();
$title = 'Gallery';
include __DIR__ . '/../layout/app.php';
?>
