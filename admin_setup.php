<?php
/**
 * Admin Setup Page
 * Access this page in your browser to check/create admin user
 */

require_once 'config/app.php';
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Load configuration
Config::load();

$message = '';
$messageType = '';
$admins = [];

try {
    $db = Database::getInstance();
    
    // Handle form submission
    if ($_POST && isset($_POST['action'])) {
        if ($_POST['action'] === 'create_admin') {
            $email = sanitize($_POST['email'] ?? '');
            $password = $_POST['password'] ?? '';
            $firstName = sanitize($_POST['first_name'] ?? '');
            $lastName = sanitize($_POST['last_name'] ?? '');
            
            if (empty($email) || empty($password) || empty($firstName) || empty($lastName)) {
                $message = 'All fields are required.';
                $messageType = 'error';
            } elseif (!validate_email($email)) {
                $message = 'Please enter a valid email address.';
                $messageType = 'error';
            } elseif (strlen($password) < 8) {
                $message = 'Password must be at least 8 characters long.';
                $messageType = 'error';
            } else {
                // Check if email already exists
                $stmt = $db->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
                $stmt->execute([$email]);
                if ($stmt->fetchColumn() > 0) {
                    $message = 'Email already exists.';
                    $messageType = 'error';
                } else {
                    // Create admin user
                    $stmt = $db->prepare("
                        INSERT INTO users (email, password_hash, first_name, last_name, role, email_verified, status) 
                        VALUES (?, ?, ?, ?, 'admin', 1, 'active')
                    ");
                    
                    $passwordHash = password_hash($password, PASSWORD_ARGON2ID);
                    
                    if ($stmt->execute([$email, $passwordHash, $firstName, $lastName])) {
                        $adminId = $db->lastInsertId();
                        
                        // Assign business plan to admin
                        $stmt = $db->prepare("
                            INSERT INTO user_subscriptions (user_id, plan_id, status, current_period_start, current_period_end) 
                            VALUES (?, 3, 'active', CURRENT_TIMESTAMP, DATE('now', '+1 year'))
                        ");
                        $stmt->execute([$adminId]);
                        
                        $message = 'Admin user created successfully! You can now login.';
                        $messageType = 'success';
                    } else {
                        $message = 'Failed to create admin user.';
                        $messageType = 'error';
                    }
                }
            }
        }
    }
    
    // Get existing admin users
    $stmt = $db->prepare("SELECT id, email, first_name, last_name, created_at, last_login FROM users WHERE role = 'admin'");
    $stmt->execute();
    $admins = $stmt->fetchAll();
    
} catch (Exception $e) {
    $message = 'Database error: ' . $e->getMessage();
    $messageType = 'error';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Setup - <?= Config::get('app.name') ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-user-shield me-2"></i>Admin Setup</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                            <div class="alert alert-<?= $messageType === 'error' ? 'danger' : 'success' ?> alert-dismissible fade show">
                                <?= htmlspecialchars($message) ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($admins)): ?>
                            <div class="mb-4">
                                <h5><i class="fas fa-users me-2"></i>Existing Admin Users</h5>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Email</th>
                                                <th>Name</th>
                                                <th>Created</th>
                                                <th>Last Login</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($admins as $admin): ?>
                                                <tr>
                                                    <td><?= $admin['id'] ?></td>
                                                    <td><?= htmlspecialchars($admin['email']) ?></td>
                                                    <td><?= htmlspecialchars($admin['first_name'] . ' ' . $admin['last_name']) ?></td>
                                                    <td><?= $admin['created_at'] ?></td>
                                                    <td><?= $admin['last_login'] ?: 'Never' ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Admin Panel Access:</strong><br>
                                    URL: <a href="<?= url('admin') ?>" target="_blank"><?= url('admin') ?></a><br>
                                    Login: <a href="<?= url('login') ?>" target="_blank"><?= url('login') ?></a>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (empty($admins)): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>No admin users found!</strong> Please create an admin user below.
                            </div>
                        <?php endif; ?>
                        
                        <h5><i class="fas fa-user-plus me-2"></i>Create New Admin User</h5>
                        <form method="POST">
                            <input type="hidden" name="action" value="create_admin">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="first_name" class="form-label">First Name</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="last_name" class="form-label">Last Name</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" name="password" required minlength="8">
                                <div class="form-text">Password must be at least 8 characters long.</div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-user-plus me-2"></i>Create Admin User
                            </button>
                        </form>
                        
                        <hr>
                        
                        <div class="text-muted">
                            <small>
                                <i class="fas fa-info-circle me-1"></i>
                                After creating an admin user, you can delete this file (admin_setup.php) for security.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
