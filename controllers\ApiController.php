<?php

class ApiController extends BaseController {
    
    public function usage() {
        $this->requireApiAuth();
        
        $userId = $this->getApiUserId();
        $usage = Auth::getUserUsage($userId);
        $subscription = Auth::getUserSubscription($userId);
        
        $this->json([
            'success' => true,
            'data' => [
                'daily_usage' => $usage['daily'],
                'monthly_usage' => $usage['monthly'],
                'daily_limit' => $subscription['daily_generations'] ?? 5,
                'monthly_limit' => $subscription['monthly_generations'] ?? 50,
                'plan' => $subscription['plan_name'] ?? 'Free'
            ]
        ]);
    }
    
    public function generate() {
        $this->requireApiAuth();
        
        $userId = $this->getApiUserId();
        
        // Check rate limiting
        $identifier = $this->getApiKeyIdentifier();
        if (!rate_limit_check($identifier, 'api_generate', 10, 60)) { // 10 requests per minute
            $this->json([
                'success' => false,
                'error' => 'Rate limit exceeded. Please try again later.'
            ], 429);
        }
        
        // Check if user can generate images
        if (!Auth::canGenerateImage($userId)) {
            $this->json([
                'success' => false,
                'error' => 'Generation limit reached for today.'
            ], 403);
        }
        
        // Get JSON input
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            $this->json([
                'success' => false,
                'error' => 'Invalid JSON input.'
            ], 400);
        }
        
        // Validate input
        $data = [
            'prompt' => sanitize($input['prompt'] ?? ''),
            'negative_prompt' => sanitize($input['negative_prompt'] ?? ''),
            'width' => (int)($input['width'] ?? 512),
            'height' => (int)($input['height'] ?? 512),
            'style' => sanitize($input['style'] ?? ''),
            'provider' => sanitize($input['provider'] ?? ''),
        ];
        
        $errors = $this->validate($data, [
            'prompt' => 'required|min:3|max:1000',
            'width' => 'required',
            'height' => 'required',
            'provider' => 'required'
        ]);
        
        if (!empty($errors)) {
            $this->json([
                'success' => false,
                'error' => 'Validation failed.',
                'details' => $errors
            ], 400);
        }
        
        // Validate dimensions
        if (!is_valid_image_dimensions($data['width'], $data['height'])) {
            $this->json([
                'success' => false,
                'error' => 'Invalid image dimensions.'
            ], 400);
        }
        
        // Check if user has API key for selected provider
        $db = Database::getInstance();
        $stmt = $db->prepare("SELECT api_key FROM user_ai_provider_keys WHERE user_id = ? AND provider = ? AND is_active = 1");
        $stmt->execute([$userId, $data['provider']]);
        $apiKeyData = $stmt->fetch();
        
        if (!$apiKeyData) {
            $this->json([
                'success' => false,
                'error' => 'No API key configured for selected provider.'
            ], 400);
        }
        
        try {
            // Create image generation record
            $stmt = $db->prepare("
                INSERT INTO generated_images (user_id, prompt, negative_prompt, width, height, style, ai_provider, file_path, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending')
            ");
            
            $filename = 'api_' . time() . '_' . uniqid() . '.png';
            $filePath = 'storage/generated/' . $filename;
            
            $stmt->execute([
                $userId,
                $data['prompt'],
                $data['negative_prompt'],
                $data['width'],
                $data['height'],
                $data['style'],
                $data['provider'],
                $filePath
            ]);
            
            $imageId = $db->lastInsertId();
            
            // Record usage
            Auth::recordUsage($userId, 'api_generation');
            
            // Start generation process (simplified for demo)
            $this->processApiGeneration($imageId, $data, decrypt($apiKeyData['api_key']));
            
            $this->json([
                'success' => true,
                'data' => [
                    'image_id' => $imageId,
                    'status' => 'pending',
                    'message' => 'Image generation started.'
                ]
            ]);
            
        } catch (Exception $e) {
            log_error('API image generation failed: ' . $e->getMessage(), $data);
            $this->json([
                'success' => false,
                'error' => 'Failed to start image generation.'
            ], 500);
        }
    }
    
    public function images() {
        $this->requireApiAuth();
        
        $userId = $this->getApiUserId();
        $db = Database::getInstance();
        
        $page = max(1, (int)($_GET['page'] ?? 1));
        $perPage = min(50, (int)($_GET['per_page'] ?? 20)); // Max 50 per page
        $offset = ($page - 1) * $perPage;
        
        $status = sanitize($_GET['status'] ?? '');
        
        // Build query
        $whereConditions = ['user_id = ?'];
        $params = [$userId];
        
        if (!empty($status)) {
            $whereConditions[] = 'status = ?';
            $params[] = $status;
        }
        
        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        
        // Get total count
        $countQuery = "SELECT COUNT(*) FROM generated_images $whereClause";
        $stmt = $db->prepare($countQuery);
        $stmt->execute($params);
        $totalImages = $stmt->fetchColumn();
        
        // Get images
        $query = "
            SELECT id, prompt, negative_prompt, width, height, style, ai_provider, 
                   file_path, status, error_message, generation_time, created_at
            FROM generated_images 
            $whereClause 
            ORDER BY created_at DESC 
            LIMIT $perPage OFFSET $offset
        ";
        $stmt = $db->prepare($query);
        $stmt->execute($params);
        $images = $stmt->fetchAll();
        
        // Convert file paths to URLs
        foreach ($images as &$image) {
            if ($image['status'] === 'completed' && !empty($image['file_path'])) {
                $image['image_url'] = url($image['file_path']);
                $image['download_url'] = url('/download/' . $image['id']);
            }
            unset($image['file_path']); // Don't expose internal paths
        }
        
        $this->json([
            'success' => true,
            'data' => $images,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $totalImages,
                'total_pages' => ceil($totalImages / $perPage)
            ]
        ]);
    }
    
    public function imageStatus($id) {
        $this->requireApiAuth();
        
        $userId = $this->getApiUserId();
        $db = Database::getInstance();
        
        $stmt = $db->prepare("
            SELECT id, status, error_message, generation_time, file_path, created_at
            FROM generated_images 
            WHERE id = ? AND user_id = ?
        ");
        $stmt->execute([$id, $userId]);
        $image = $stmt->fetch();
        
        if (!$image) {
            $this->json([
                'success' => false,
                'error' => 'Image not found.'
            ], 404);
        }
        
        $response = [
            'success' => true,
            'data' => [
                'id' => $image['id'],
                'status' => $image['status'],
                'created_at' => $image['created_at']
            ]
        ];
        
        if ($image['status'] === 'completed') {
            $response['data']['image_url'] = url($image['file_path']);
            $response['data']['download_url'] = url('/download/' . $image['id']);
            $response['data']['generation_time'] = $image['generation_time'];
        } elseif ($image['status'] === 'failed') {
            $response['data']['error'] = $image['error_message'];
        }
        
        $this->json($response);
    }
    
    public function testKey() {
        $this->requireApiAuth();
        
        $input = json_decode(file_get_contents('php://input'), true);
        $provider = sanitize($input['provider'] ?? '');
        $keyId = (int)($input['keyId'] ?? 0);
        
        if (empty($provider)) {
            $this->json([
                'success' => false,
                'error' => 'Provider is required.'
            ], 400);
        }
        
        $userId = $this->getApiUserId();
        $db = Database::getInstance();
        
        // Get API key
        $stmt = $db->prepare("SELECT api_key FROM user_ai_provider_keys WHERE id = ? AND user_id = ? AND provider = ?");
        $stmt->execute([$keyId, $userId, $provider]);
        $apiKeyData = $stmt->fetch();
        
        if (!$apiKeyData) {
            $this->json([
                'success' => false,
                'error' => 'API key not found.'
            ], 404);
        }
        
        try {
            // Test the API key (simplified)
            $apiKey = decrypt($apiKeyData['api_key']);
            $testResult = $this->testProviderApiKey($provider, $apiKey);
            
            if ($testResult['success']) {
                // Update last used timestamp
                $stmt = $db->prepare("UPDATE user_ai_provider_keys SET last_used = CURRENT_TIMESTAMP WHERE id = ?");
                $stmt->execute([$keyId]);
                
                $this->json([
                    'success' => true,
                    'message' => 'API key is valid and working.'
                ]);
            } else {
                $this->json([
                    'success' => false,
                    'error' => 'API key test failed: ' . $testResult['error']
                ]);
            }
            
        } catch (Exception $e) {
            log_error('API key test failed: ' . $e->getMessage());
            $this->json([
                'success' => false,
                'error' => 'Failed to test API key.'
            ], 500);
        }
    }
    
    private function requireApiAuth() {
        $apiKey = $this->getApiKeyFromRequest();
        
        if (empty($apiKey)) {
            $this->json([
                'success' => false,
                'error' => 'API key required.'
            ], 401);
        }
        
        $db = Database::getInstance();
        $stmt = $db->prepare("SELECT user_id FROM user_api_keys WHERE api_key = ? AND is_active = 1");
        $stmt->execute([$apiKey]);
        $result = $stmt->fetch();
        
        if (!$result) {
            $this->json([
                'success' => false,
                'error' => 'Invalid API key.'
            ], 401);
        }
        
        // Store user ID for later use
        $this->apiUserId = $result['user_id'];
        $this->apiKey = $apiKey;
        
        // Update last used timestamp
        $stmt = $db->prepare("UPDATE user_api_keys SET last_used = CURRENT_TIMESTAMP WHERE api_key = ?");
        $stmt->execute([$apiKey]);
    }
    
    private function getApiKeyFromRequest() {
        // Check Authorization header
        $headers = getallheaders();
        if (isset($headers['Authorization'])) {
            if (preg_match('/Bearer\s+(.*)$/i', $headers['Authorization'], $matches)) {
                return $matches[1];
            }
        }
        
        // Check X-API-Key header
        if (isset($headers['X-API-Key'])) {
            return $headers['X-API-Key'];
        }
        
        // Check query parameter
        return $_GET['api_key'] ?? '';
    }
    
    private function getApiUserId() {
        return $this->apiUserId ?? null;
    }
    
    private function getApiKeyIdentifier() {
        return 'api_key_' . substr(md5($this->apiKey ?? ''), 0, 8);
    }
    
    private function processApiGeneration($imageId, $data, $apiKey) {
        // This would typically be handled by a background job queue
        // For demo purposes, we'll simulate immediate processing
        
        $db = Database::getInstance();
        
        try {
            // Update status to generating
            $stmt = $db->prepare("UPDATE generated_images SET status = 'generating' WHERE id = ?");
            $stmt->execute([$imageId]);
            
            // Simulate API call
            sleep(1);
            
            // Simulate successful generation
            $stmt = $db->prepare("SELECT file_path FROM generated_images WHERE id = ?");
            $stmt->execute([$imageId]);
            $filePath = $stmt->fetchColumn();
            
            // Create a placeholder image
            $imageData = file_get_contents('https://picsum.photos/' . $data['width'] . '/' . $data['height']);
            file_put_contents($filePath, $imageData);
            
            // Update record
            $stmt = $db->prepare("
                UPDATE generated_images 
                SET status = 'completed', file_size = ?, generation_time = ? 
                WHERE id = ?
            ");
            $stmt->execute([strlen($imageData), 2.5, $imageId]);
            
        } catch (Exception $e) {
            $stmt = $db->prepare("UPDATE generated_images SET status = 'failed', error_message = ? WHERE id = ?");
            $stmt->execute([$e->getMessage(), $imageId]);
        }
    }
    
    private function testProviderApiKey($provider, $apiKey) {
        // Simulate API key testing
        // In real implementation, this would make actual API calls to test the keys
        
        if (strlen($apiKey) < 10) {
            return ['success' => false, 'error' => 'API key too short'];
        }
        
        return ['success' => true];
    }
}
