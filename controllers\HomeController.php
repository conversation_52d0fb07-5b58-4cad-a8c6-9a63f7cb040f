<?php

class HomeController extends BaseController {

    public function index() {
        // If user is logged in, redirect to dashboard
        if (Auth::check()) {
            $this->redirect(url('dashboard'));
        }

        // Get some statistics for the landing page
        $db = Database::getInstance();

        // Total images generated
        $stmt = $db->query("SELECT COUNT(*) FROM generated_images WHERE status = 'completed'");
        $totalImages = $stmt->fetchColumn();

        // Total users
        $stmt = $db->query("SELECT COUNT(*) FROM users WHERE status = 'active'");
        $totalUsers = $stmt->fetchColumn();

        // Get subscription plans for pricing table
        $stmt = $db->query("SELECT * FROM subscription_plans WHERE is_active = 1 ORDER BY price ASC");
        $plans = $stmt->fetchAll();

        // Get recent sample images (if any)
        $stmt = $db->query("
            SELECT file_path, prompt, width, height
            FROM generated_images
            WHERE status = 'completed'
            ORDER BY created_at DESC
            LIMIT 6
        ");
        $sampleImages = $stmt->fetchAll();

        $this->view('home/index', [
            'totalImages' => $totalImages,
            'totalUsers' => $totalUsers,
            'plans' => $plans,
            'sampleImages' => $sampleImages
        ]);
    }

    public function privacy() {
        $this->view('legal/privacy');
    }

    public function terms() {
        $this->view('legal/terms');
    }

    public function contact() {
        $this->view('legal/contact');
    }

    public function submitContact() {
        $this->validateCsrf();

        $data = [
            'name' => sanitize($_POST['name'] ?? ''),
            'email' => sanitize($_POST['email'] ?? ''),
            'subject' => sanitize($_POST['subject'] ?? ''),
            'message' => sanitize($_POST['message'] ?? '')
        ];

        $errors = $this->validate($data, [
            'name' => 'required|min:2|max:100',
            'email' => 'required|email',
            'subject' => 'required',
            'message' => 'required|min:10|max:2000'
        ]);

        if (!empty($errors)) {
            $this->json(['success' => false, 'message' => 'Please check your input and try again.'], 400);
        }

        try {
            // Here you would typically send an email or save to database
            // For now, we'll just log it
            log_activity(null, 'contact_form_submitted', $data);

            $this->json(['success' => true, 'message' => 'Message sent successfully!']);
        } catch (Exception $e) {
            log_error('Contact form submission failed: ' . $e->getMessage(), $data);
            $this->json(['success' => false, 'message' => 'Failed to send message. Please try again.'], 500);
        }
    }
}
