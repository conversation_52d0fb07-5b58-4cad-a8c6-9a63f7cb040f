<?php
ob_start();
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 fw-bold">Subscription Plans</h1>
                    <p class="text-muted">Manage subscription plans and pricing</p>
                </div>
                <div>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#planModal">
                        <i class="fas fa-plus me-2"></i>Add New Plan
                    </button>
                    <a href="<?= url('admin') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Plans Grid -->
    <div class="row g-4">
        <?php foreach ($plans as $plan): ?>
            <div class="col-lg-4 col-md-6">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-header bg-white border-0 py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><?= htmlspecialchars($plan['name']) ?></h5>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <button class="dropdown-item" onclick="editPlan(<?= htmlspecialchars(json_encode($plan)) ?>)">
                                            <i class="fas fa-edit me-2"></i>Edit
                                        </button>
                                    </li>
                                    <li>
                                        <button class="dropdown-item text-danger" onclick="deletePlan(<?= $plan['id'] ?>, '<?= htmlspecialchars($plan['name']) ?>')">
                                            <i class="fas fa-trash me-2"></i>Delete
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <div class="h2 fw-bold text-primary">$<?= number_format($plan['price'], 2) ?></div>
                            <small class="text-muted">per <?= $plan['billing_cycle'] ?></small>
                        </div>
                        
                        <p class="text-muted"><?= htmlspecialchars($plan['description']) ?></p>
                        
                        <ul class="list-unstyled mb-3">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <?= number_format($plan['daily_generations']) ?> images per day
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <?= number_format($plan['monthly_generations']) ?> images per month
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Up to <?= $plan['max_bulk_size'] ?> bulk generations
                            </li>
                            <?php
                            $features = json_decode($plan['features'], true);
                            if ($features):
                                foreach ($features as $feature):
                            ?>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <?= htmlspecialchars($feature) ?>
                                </li>
                            <?php
                                endforeach;
                            endif;
                            ?>
                        </ul>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <?php if ($plan['is_active']): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">Inactive</span>
                                <?php endif; ?>
                            </div>
                            <div class="text-muted small">
                                <?= $plan['subscriber_count'] ?> subscribers
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>

<!-- Plan Modal -->
<div class="modal fade" id="planModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="planModalTitle">Add New Plan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="planForm" method="POST" action="<?= url('admin/plans') ?>">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                    <input type="hidden" name="plan_id" id="plan_id">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Plan Name *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="price" class="form-label">Price *</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="billing_cycle" class="form-label">Billing Cycle *</label>
                            <select class="form-select" id="billing_cycle" name="billing_cycle" required>
                                <option value="monthly">Monthly</option>
                                <option value="yearly">Yearly</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="daily_generations" class="form-label">Daily Generations *</label>
                            <input type="number" class="form-control" id="daily_generations" name="daily_generations" min="0" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="monthly_generations" class="form-label">Monthly Generations *</label>
                            <input type="number" class="form-control" id="monthly_generations" name="monthly_generations" min="0" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="max_bulk_size" class="form-label">Max Bulk Size</label>
                            <input type="number" class="form-control" id="max_bulk_size" name="max_bulk_size" min="1" value="10">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="stripe_price_id" class="form-label">Stripe Price ID</label>
                            <input type="text" class="form-control" id="stripe_price_id" name="stripe_price_id" placeholder="price_...">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="features" class="form-label">Features (one per line)</label>
                        <textarea class="form-control" id="features" name="features" rows="4" 
                                  placeholder="Basic image generation&#10;Standard resolution&#10;Email support"></textarea>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">
                            Active (visible to users)
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Plan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editPlan(plan) {
    document.getElementById('planModalTitle').textContent = 'Edit Plan';
    document.getElementById('plan_id').value = plan.id;
    document.getElementById('name').value = plan.name;
    document.getElementById('description').value = plan.description;
    document.getElementById('price').value = plan.price;
    document.getElementById('billing_cycle').value = plan.billing_cycle;
    document.getElementById('daily_generations').value = plan.daily_generations;
    document.getElementById('monthly_generations').value = plan.monthly_generations;
    document.getElementById('max_bulk_size').value = plan.max_bulk_size;
    document.getElementById('stripe_price_id').value = plan.stripe_price_id || '';
    document.getElementById('is_active').checked = plan.is_active == 1;
    
    // Handle features
    if (plan.features) {
        try {
            const features = JSON.parse(plan.features);
            document.getElementById('features').value = features.join('\n');
        } catch (e) {
            document.getElementById('features').value = '';
        }
    }
    
    const modal = new bootstrap.Modal(document.getElementById('planModal'));
    modal.show();
}

function deletePlan(planId, planName) {
    if (confirm(`Are you sure you want to delete the "${planName}" plan? This action cannot be undone.`)) {
        fetch(`<?= url('admin/plans') ?>/${planId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': '<?= csrf_token() ?>'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Utils.showToast('Plan deleted successfully', 'success');
                location.reload();
            } else {
                Utils.showToast(data.message || 'Failed to delete plan', 'danger');
            }
        })
        .catch(error => {
            Utils.showToast('An error occurred', 'danger');
            console.error('Delete error:', error);
        });
    }
}

// Reset form when modal is hidden
document.getElementById('planModal').addEventListener('hidden.bs.modal', function () {
    document.getElementById('planModalTitle').textContent = 'Add New Plan';
    document.getElementById('planForm').reset();
    document.getElementById('plan_id').value = '';
});
</script>

<?php
$content = ob_get_clean();
$title = 'Subscription Plans';
include __DIR__ . '/../layout/app.php';
?>
