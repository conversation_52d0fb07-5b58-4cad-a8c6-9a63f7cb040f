<?php

class Webhook<PERSON>ontroller extends BaseController {
    
    public function stripe() {
        // Handle Stripe webhooks
        
        $payload = file_get_contents('php://input');
        $sigHeader = $_SERVER['HTTP_STRIPE_SIGNATURE'] ?? '';
        
        try {
            // Verify webhook signature
            $event = $this->verifyStripeWebhook($payload, $sigHeader);
            
            if (!$event) {
                http_response_code(400);
                exit('Invalid signature');
            }
            
            // Handle different event types
            switch ($event['type']) {
                case 'checkout.session.completed':
                    $this->handleCheckoutCompleted($event['data']['object']);
                    break;
                    
                case 'invoice.payment_succeeded':
                    $this->handlePaymentSucceeded($event['data']['object']);
                    break;
                    
                case 'invoice.payment_failed':
                    $this->handlePaymentFailed($event['data']['object']);
                    break;
                    
                case 'customer.subscription.updated':
                    $this->handleSubscriptionUpdated($event['data']['object']);
                    break;
                    
                case 'customer.subscription.deleted':
                    $this->handleSubscriptionDeleted($event['data']['object']);
                    break;
                    
                default:
                    log_error('Unhandled Stripe webhook event: ' . $event['type']);
                    break;
            }
            
            // Log successful webhook processing
            log_activity(0, 'stripe_webhook_processed', [
                'event_type' => $event['type'],
                'event_id' => $event['id']
            ]);
            
            http_response_code(200);
            echo json_encode(['status' => 'success']);
            
        } catch (Exception $e) {
            log_error('Stripe webhook error: ' . $e->getMessage(), [
                'payload' => $payload,
                'signature' => $sigHeader
            ]);
            
            http_response_code(400);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }
    
    public function aiProvider() {
        // Handle webhooks from AI providers (if they support them)
        
        $provider = $_GET['provider'] ?? '';
        $payload = file_get_contents('php://input');
        
        if (empty($provider)) {
            http_response_code(400);
            exit('Provider not specified');
        }
        
        try {
            switch ($provider) {
                case 'together_ai':
                    $this->handleTogetherAIWebhook($payload);
                    break;
                    
                case 'runware':
                    $this->handleRunwareWebhook($payload);
                    break;
                    
                case 'replicate':
                    $this->handleReplicateWebhook($payload);
                    break;
                    
                case 'fal':
                    $this->handleFalWebhook($payload);
                    break;
                    
                default:
                    log_error('Unknown AI provider webhook: ' . $provider);
                    http_response_code(400);
                    exit('Unknown provider');
            }
            
            http_response_code(200);
            echo json_encode(['status' => 'success']);
            
        } catch (Exception $e) {
            log_error('AI provider webhook error: ' . $e->getMessage(), [
                'provider' => $provider,
                'payload' => $payload
            ]);
            
            http_response_code(400);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }
    
    private function verifyStripeWebhook($payload, $sigHeader) {
        $webhookSecret = Config::get('stripe.webhook_secret');
        
        if (empty($webhookSecret)) {
            throw new Exception('Webhook secret not configured');
        }
        
        // In real implementation, use Stripe's signature verification
        // For now, we'll simulate it
        if (empty($sigHeader)) {
            return false;
        }
        
        // Parse the JSON payload
        $event = json_decode($payload, true);
        
        if (!$event) {
            throw new Exception('Invalid JSON payload');
        }
        
        return $event;
    }
    
    private function handleCheckoutCompleted($session) {
        $db = Database::getInstance();
        
        try {
            // Extract customer and subscription info from session
            $customerId = $session['customer'] ?? '';
            $subscriptionId = $session['subscription'] ?? '';
            $clientReferenceId = $session['client_reference_id'] ?? '';
            
            if (empty($clientReferenceId)) {
                throw new Exception('No client reference ID in session');
            }
            
            // Find user by reference ID (should be user ID)
            $userId = (int)$clientReferenceId;
            
            $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
            
            if (!$user) {
                throw new Exception('User not found: ' . $userId);
            }
            
            // Get plan info from session metadata
            $planId = $session['metadata']['plan_id'] ?? 2; // Default to Pro plan
            
            $db->beginTransaction();
            
            // Deactivate current subscription
            $stmt = $db->prepare("UPDATE user_subscriptions SET status = 'canceled' WHERE user_id = ? AND status = 'active'");
            $stmt->execute([$userId]);
            
            // Create new subscription
            $stmt = $db->prepare("
                INSERT INTO user_subscriptions (user_id, plan_id, stripe_subscription_id, status, current_period_start, current_period_end) 
                VALUES (?, ?, ?, 'active', CURRENT_TIMESTAMP, DATE('now', '+1 month'))
            ");
            $stmt->execute([$userId, $planId, $subscriptionId]);
            
            $db->commit();
            
            log_activity($userId, 'subscription_activated', [
                'plan_id' => $planId,
                'stripe_subscription_id' => $subscriptionId
            ]);
            
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    }
    
    private function handlePaymentSucceeded($invoice) {
        $subscriptionId = $invoice['subscription'] ?? '';
        
        if (empty($subscriptionId)) {
            return;
        }
        
        $db = Database::getInstance();
        
        try {
            // Find subscription
            $stmt = $db->prepare("SELECT user_id FROM user_subscriptions WHERE stripe_subscription_id = ?");
            $stmt->execute([$subscriptionId]);
            $subscription = $stmt->fetch();
            
            if (!$subscription) {
                throw new Exception('Subscription not found: ' . $subscriptionId);
            }
            
            // Update subscription period
            $periodStart = date('Y-m-d H:i:s', $invoice['period_start']);
            $periodEnd = date('Y-m-d H:i:s', $invoice['period_end']);
            
            $stmt = $db->prepare("
                UPDATE user_subscriptions 
                SET status = 'active', current_period_start = ?, current_period_end = ?, cancel_at_period_end = 0
                WHERE stripe_subscription_id = ?
            ");
            $stmt->execute([$periodStart, $periodEnd, $subscriptionId]);
            
            log_activity($subscription['user_id'], 'payment_succeeded', [
                'invoice_id' => $invoice['id'],
                'amount' => $invoice['amount_paid']
            ]);
            
        } catch (Exception $e) {
            throw $e;
        }
    }
    
    private function handlePaymentFailed($invoice) {
        $subscriptionId = $invoice['subscription'] ?? '';
        
        if (empty($subscriptionId)) {
            return;
        }
        
        $db = Database::getInstance();
        
        try {
            // Find subscription
            $stmt = $db->prepare("SELECT user_id FROM user_subscriptions WHERE stripe_subscription_id = ?");
            $stmt->execute([$subscriptionId]);
            $subscription = $stmt->fetch();
            
            if (!$subscription) {
                throw new Exception('Subscription not found: ' . $subscriptionId);
            }
            
            // Update subscription status
            $stmt = $db->prepare("UPDATE user_subscriptions SET status = 'past_due' WHERE stripe_subscription_id = ?");
            $stmt->execute([$subscriptionId]);
            
            log_activity($subscription['user_id'], 'payment_failed', [
                'invoice_id' => $invoice['id'],
                'amount' => $invoice['amount_due']
            ]);
            
            // TODO: Send email notification to user
            
        } catch (Exception $e) {
            throw $e;
        }
    }
    
    private function handleSubscriptionUpdated($subscription) {
        $subscriptionId = $subscription['id'];
        $status = $subscription['status'];
        
        $db = Database::getInstance();
        
        try {
            // Find subscription
            $stmt = $db->prepare("SELECT user_id FROM user_subscriptions WHERE stripe_subscription_id = ?");
            $stmt->execute([$subscriptionId]);
            $userSubscription = $stmt->fetch();
            
            if (!$userSubscription) {
                throw new Exception('Subscription not found: ' . $subscriptionId);
            }
            
            // Map Stripe status to our status
            $ourStatus = $this->mapStripeStatus($status);
            
            // Update subscription
            $stmt = $db->prepare("UPDATE user_subscriptions SET status = ? WHERE stripe_subscription_id = ?");
            $stmt->execute([$ourStatus, $subscriptionId]);
            
            log_activity($userSubscription['user_id'], 'subscription_updated', [
                'stripe_status' => $status,
                'our_status' => $ourStatus
            ]);
            
        } catch (Exception $e) {
            throw $e;
        }
    }
    
    private function handleSubscriptionDeleted($subscription) {
        $subscriptionId = $subscription['id'];
        
        $db = Database::getInstance();
        
        try {
            // Find subscription
            $stmt = $db->prepare("SELECT user_id FROM user_subscriptions WHERE stripe_subscription_id = ?");
            $stmt->execute([$subscriptionId]);
            $userSubscription = $stmt->fetch();
            
            if (!$userSubscription) {
                throw new Exception('Subscription not found: ' . $subscriptionId);
            }
            
            // Cancel subscription
            $stmt = $db->prepare("UPDATE user_subscriptions SET status = 'canceled' WHERE stripe_subscription_id = ?");
            $stmt->execute([$subscriptionId]);
            
            // Assign free plan
            $stmt = $db->prepare("
                INSERT INTO user_subscriptions (user_id, plan_id, status, current_period_start, current_period_end) 
                VALUES (?, 1, 'active', CURRENT_TIMESTAMP, DATE('now', '+1 month'))
            ");
            $stmt->execute([$userSubscription['user_id']]);
            
            log_activity($userSubscription['user_id'], 'subscription_canceled', [
                'stripe_subscription_id' => $subscriptionId
            ]);
            
        } catch (Exception $e) {
            throw $e;
        }
    }
    
    private function handleTogetherAIWebhook($payload) {
        // Handle Together AI webhooks (if they support them)
        $data = json_decode($payload, true);
        
        if (!$data) {
            throw new Exception('Invalid JSON payload');
        }
        
        // Process webhook data
        log_activity(0, 'together_ai_webhook', $data);
    }
    
    private function handleRunwareWebhook($payload) {
        // Handle Runware webhooks
        $data = json_decode($payload, true);
        
        if (!$data) {
            throw new Exception('Invalid JSON payload');
        }
        
        // Process webhook data
        log_activity(0, 'runware_webhook', $data);
    }
    
    private function handleReplicateWebhook($payload) {
        // Handle Replicate webhooks
        $data = json_decode($payload, true);
        
        if (!$data) {
            throw new Exception('Invalid JSON payload');
        }
        
        // Process webhook data - Replicate sends prediction updates
        if (isset($data['id']) && isset($data['status'])) {
            $this->updateImageGenerationStatus($data);
        }
        
        log_activity(0, 'replicate_webhook', $data);
    }
    
    private function handleFalWebhook($payload) {
        // Handle FAL AI webhooks
        $data = json_decode($payload, true);
        
        if (!$data) {
            throw new Exception('Invalid JSON payload');
        }
        
        // Process webhook data
        log_activity(0, 'fal_webhook', $data);
    }
    
    private function updateImageGenerationStatus($data) {
        // Update image generation status based on webhook data
        $db = Database::getInstance();
        
        $predictionId = $data['id'];
        $status = $data['status'];
        
        try {
            // Find image by prediction ID (you'd need to store this when creating the prediction)
            $stmt = $db->prepare("SELECT id FROM generated_images WHERE external_id = ?");
            $stmt->execute([$predictionId]);
            $image = $stmt->fetch();
            
            if ($image) {
                $ourStatus = $this->mapPredictionStatus($status);
                
                if ($status === 'succeeded' && isset($data['output'])) {
                    // Download and save the generated image
                    $imageUrl = $data['output'][0] ?? $data['output'];
                    $this->downloadAndSaveImage($image['id'], $imageUrl);
                } elseif ($status === 'failed') {
                    $errorMessage = $data['error'] ?? 'Generation failed';
                    $stmt = $db->prepare("UPDATE generated_images SET status = 'failed', error_message = ? WHERE id = ?");
                    $stmt->execute([$errorMessage, $image['id']]);
                }
            }
            
        } catch (Exception $e) {
            log_error('Failed to update image status: ' . $e->getMessage(), $data);
        }
    }
    
    private function mapStripeStatus($stripeStatus) {
        $statusMap = [
            'active' => 'active',
            'canceled' => 'canceled',
            'past_due' => 'past_due',
            'unpaid' => 'unpaid',
            'incomplete' => 'active',
            'incomplete_expired' => 'canceled'
        ];
        
        return $statusMap[$stripeStatus] ?? 'active';
    }
    
    private function mapPredictionStatus($predictionStatus) {
        $statusMap = [
            'starting' => 'pending',
            'processing' => 'generating',
            'succeeded' => 'completed',
            'failed' => 'failed',
            'canceled' => 'failed'
        ];
        
        return $statusMap[$predictionStatus] ?? 'pending';
    }
    
    private function downloadAndSaveImage($imageId, $imageUrl) {
        $db = Database::getInstance();
        
        try {
            // Get image record
            $stmt = $db->prepare("SELECT file_path FROM generated_images WHERE id = ?");
            $stmt->execute([$imageId]);
            $image = $stmt->fetch();
            
            if (!$image) {
                throw new Exception('Image record not found');
            }
            
            // Download image
            $imageData = file_get_contents($imageUrl);
            
            if ($imageData === false) {
                throw new Exception('Failed to download image');
            }
            
            // Save to file
            file_put_contents($image['file_path'], $imageData);
            
            // Update database
            $stmt = $db->prepare("
                UPDATE generated_images 
                SET status = 'completed', file_size = ?, generation_time = ? 
                WHERE id = ?
            ");
            $stmt->execute([strlen($imageData), null, $imageId]);
            
        } catch (Exception $e) {
            // Mark as failed
            $stmt = $db->prepare("UPDATE generated_images SET status = 'failed', error_message = ? WHERE id = ?");
            $stmt->execute([$e->getMessage(), $imageId]);
            
            throw $e;
        }
    }
}
