<?php

/**
 * Authentication helper functions
 */

class Auth {
    public static function check() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }

    public static function user() {
        if (!self::check()) {
            return null;
        }

        $db = Database::getInstance();
        $stmt = $db->prepare("SELECT * FROM users WHERE id = ? AND status = 'active'");
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch();
    }

    public static function id() {
        return $_SESSION['user_id'] ?? null;
    }

    public static function login($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_role'] = $user['role'];

        // Update last login
        $db = Database::getInstance();
        $stmt = $db->prepare("UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?");
        $stmt->execute([$user['id']]);

        log_activity($user['id'], 'login');
    }

    public static function logout() {
        $userId = self::id();
        if ($userId) {
            log_activity($userId, 'logout');
        }

        session_destroy();
        session_start();
    }

    public static function isAdmin() {
        return self::check() && $_SESSION['user_role'] === 'admin';
    }

    public static function requireAuth() {
        if (!self::check()) {
            flash('error', 'Please log in to access this page.');
            redirect('/login');
        }
    }

    public static function requireAdmin() {
        self::requireAuth();
        if (!self::isAdmin()) {
            flash('error', 'Access denied. Admin privileges required.');
            redirect('/dashboard');
        }
    }

    public static function guest() {
        if (self::check()) {
            redirect('/dashboard');
        }
    }

    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_ARGON2ID);
    }

    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }

    public static function generateEmailVerificationToken() {
        return generate_random_string(64);
    }

    public static function generatePasswordResetToken() {
        return generate_random_string(64);
    }

    public static function sendVerificationEmail($user) {
        $token = self::generateEmailVerificationToken();

        // Save token to database
        $db = Database::getInstance();
        $stmt = $db->prepare("UPDATE users SET email_verification_token = ? WHERE id = ?");
        $stmt->execute([$token, $user['id']]);

        // Send email
        $verificationUrl = Config::get('app.url') . "/verify-email?token=$token";
        $subject = "Verify your email address";
        $message = "
            <h2>Welcome to " . Config::get('app.name') . "!</h2>
            <p>Please click the link below to verify your email address:</p>
            <p><a href='$verificationUrl'>Verify Email Address</a></p>
            <p>If you didn't create an account, please ignore this email.</p>
        ";

        return self::sendEmail($user['email'], $subject, $message);
    }

    public static function sendPasswordResetEmail($user) {
        $token = self::generatePasswordResetToken();
        $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));

        // Save token to database
        $db = Database::getInstance();
        $stmt = $db->prepare("UPDATE users SET password_reset_token = ?, password_reset_expires = ? WHERE id = ?");
        $stmt->execute([$token, $expires, $user['id']]);

        // Send email
        $resetUrl = Config::get('app.url') . "/reset-password?token=$token";
        $subject = "Reset your password";
        $message = "
            <h2>Password Reset Request</h2>
            <p>You requested a password reset. Click the link below to reset your password:</p>
            <p><a href='$resetUrl'>Reset Password</a></p>
            <p>This link will expire in 1 hour.</p>
            <p>If you didn't request this, please ignore this email.</p>
        ";

        return self::sendEmail($user['email'], $subject, $message);
    }

    private static function sendEmail($to, $subject, $message) {
        // For now, we'll use PHP's mail function
        // In production, you should use PHPMailer or similar
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: ' . Config::get('mail.from_name') . ' <' . Config::get('mail.from_address') . '>',
            'Reply-To: ' . Config::get('mail.from_address'),
            'X-Mailer: PHP/' . phpversion()
        ];

        return mail($to, $subject, $message, implode("\r\n", $headers));
    }

    public static function getUserSubscription($userId = null) {
        if ($userId === null) {
            $userId = self::id();
        }

        if (!$userId) {
            return null;
        }

        $db = Database::getInstance();
        $stmt = $db->prepare("
            SELECT us.*, sp.name, sp.name as plan_name, sp.description, sp.billing_cycle,
                   sp.daily_generations, sp.monthly_generations, sp.max_bulk_size,
                   sp.features, sp.price
            FROM user_subscriptions us
            JOIN subscription_plans sp ON us.plan_id = sp.id
            WHERE us.user_id = ? AND us.status = 'active'
            ORDER BY us.created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    }

    public static function getUserUsage($userId = null, $date = null) {
        if ($userId === null) {
            $userId = self::id();
        }

        if ($date === null) {
            $date = date('Y-m-d');
        }

        $db = Database::getInstance();

        // Daily usage
        $stmt = $db->prepare("
            SELECT SUM(resource_used) as daily_usage
            FROM usage_logs
            WHERE user_id = ? AND date = ?
        ");
        $stmt->execute([$userId, $date]);
        $dailyUsage = $stmt->fetchColumn() ?: 0;

        // Monthly usage
        $monthStart = date('Y-m-01');
        $stmt = $db->prepare("
            SELECT SUM(resource_used) as monthly_usage
            FROM usage_logs
            WHERE user_id = ? AND date >= ?
        ");
        $stmt->execute([$userId, $monthStart]);
        $monthlyUsage = $stmt->fetchColumn() ?: 0;

        return [
            'daily' => $dailyUsage,
            'monthly' => $monthlyUsage
        ];
    }

    public static function canGenerateImage($userId = null) {
        $subscription = self::getUserSubscription($userId);
        if (!$subscription) {
            return false;
        }

        $usage = self::getUserUsage($userId);

        return $usage['daily'] < $subscription['daily_generations'] &&
               $usage['monthly'] < $subscription['monthly_generations'];
    }

    public static function recordUsage($userId, $action, $resourceUsed = 1) {
        $db = Database::getInstance();
        $stmt = $db->prepare("
            INSERT INTO usage_logs (user_id, action, resource_used, date)
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([$userId, $action, $resourceUsed, date('Y-m-d')]);
    }
}
