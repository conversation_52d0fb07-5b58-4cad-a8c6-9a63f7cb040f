<?php
ob_start();
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-5">
                <h1 class="fw-bold">Contact Us</h1>
                <p class="lead text-muted">Get in touch with our team. We're here to help!</p>
            </div>
            
            <div class="row g-4">
                <!-- Contact Form -->
                <div class="col-lg-8">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-4">
                            <h3 class="fw-bold mb-4">Send us a Message</h3>
                            
                            <form id="contact-form" method="POST" action="<?= url('contact') ?>">
                                <input type="hidden" name="csrf_token" value="<?= csrf_token() ?>">
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">Name *</label>
                                        <input type="text" class="form-control" id="name" name="name" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email *</label>
                                        <input type="email" class="form-control" id="email" name="email" required>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="subject" class="form-label">Subject *</label>
                                    <select class="form-select" id="subject" name="subject" required>
                                        <option value="">Select a subject</option>
                                        <option value="general">General Inquiry</option>
                                        <option value="technical">Technical Support</option>
                                        <option value="billing">Billing Question</option>
                                        <option value="feature">Feature Request</option>
                                        <option value="bug">Bug Report</option>
                                        <option value="partnership">Partnership</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="message" class="form-label">Message *</label>
                                    <textarea class="form-control" id="message" name="message" rows="6" required 
                                              placeholder="Please describe your inquiry in detail..."></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>Send Message
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Information -->
                <div class="col-lg-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-4">
                            <h3 class="fw-bold mb-4">Get in Touch</h3>
                            
                            <div class="contact-info">
                                <div class="mb-4">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-envelope text-primary me-3"></i>
                                        <h6 class="mb-0">Email</h6>
                                    </div>
                                    <p class="text-muted ms-4">support@<?= $_SERVER['HTTP_HOST'] ?? 'example.com' ?></p>
                                </div>
                                
                                <div class="mb-4">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-clock text-primary me-3"></i>
                                        <h6 class="mb-0">Response Time</h6>
                                    </div>
                                    <p class="text-muted ms-4">We typically respond within 24 hours</p>
                                </div>
                                
                                <div class="mb-4">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-life-ring text-primary me-3"></i>
                                        <h6 class="mb-0">Support Hours</h6>
                                    </div>
                                    <p class="text-muted ms-4">Monday - Friday<br>9:00 AM - 6:00 PM (UTC)</p>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <h5 class="fw-bold mb-3">Quick Help</h5>
                            <div class="d-grid gap-2">
                                <a href="<?= url('docs') ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-book me-2"></i>Documentation
                                </a>
                                <a href="<?= url('faq') ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-question-circle me-2"></i>FAQ
                                </a>
                                <a href="<?= url('status') ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-heartbeat me-2"></i>Service Status
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- FAQ Section -->
                    <div class="card border-0 shadow-sm mt-4">
                        <div class="card-body p-4">
                            <h5 class="fw-bold mb-3">Common Questions</h5>
                            
                            <div class="accordion accordion-flush" id="faqAccordion">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                            How do I get API keys?
                                        </button>
                                    </h2>
                                    <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            You need to sign up with AI providers like Together AI, Runware, Replicate, or FAL AI to get your API keys.
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                            Are my API keys secure?
                                        </button>
                                    </h2>
                                    <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            Yes, all API keys are encrypted using industry-standard encryption before being stored in our database.
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                            Can I cancel my subscription?
                                        </button>
                                    </h2>
                                    <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            Yes, you can cancel your subscription at any time from your account settings. You'll retain access until the end of your billing period.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('contact-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const form = e.target;
    const submitBtn = form.querySelector('button[type="submit"]');
    const formData = new FormData(form);
    
    try {
        Utils.showLoading(submitBtn, 'Sending...');
        
        const response = await fetch(form.action, {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            Utils.showToast('Message sent successfully! We\'ll get back to you soon.', 'success');
            form.reset();
        } else {
            Utils.showToast(result.message || 'Failed to send message. Please try again.', 'danger');
        }
    } catch (error) {
        Utils.showToast('An error occurred. Please try again later.', 'danger');
        console.error('Contact form error:', error);
    } finally {
        Utils.hideLoading(submitBtn);
    }
});
</script>

<?php
$content = ob_get_clean();
$title = 'Contact Us';
include __DIR__ . '/../layout/app.php';
?>
