<?php
// Simple test to check if the application is working

echo "<h1>AI Image Generator - System Test</h1>";

// Test 1: Check PHP version
echo "<h2>1. PHP Version</h2>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "Required: 8.1+ " . (version_compare(PHP_VERSION, '8.1.0') >= 0 ? "✅" : "❌") . "<br><br>";

// Test 2: Check required extensions
echo "<h2>2. Required Extensions</h2>";
$requiredExtensions = ['pdo', 'pdo_sqlite', 'openssl', 'json', 'mbstring', 'curl'];
foreach ($requiredExtensions as $ext) {
    echo "$ext: " . (extension_loaded($ext) ? "✅" : "❌") . "<br>";
}
echo "<br>";

// Test 3: Check file permissions
echo "<h2>3. File Permissions</h2>";
$directories = ['storage', 'storage/uploads', 'storage/generated', 'storage/logs', 'database'];
foreach ($directories as $dir) {
    echo "$dir: " . (is_writable($dir) ? "✅ Writable" : "❌ Not writable") . "<br>";
}
echo "<br>";

// Test 4: Check configuration
echo "<h2>4. Configuration</h2>";
try {
    require_once 'config/app.php';
    Config::load();
    echo "Config loaded: ✅<br>";
    echo "App Name: " . Config::get('app.name') . "<br>";
    echo "App URL: " . Config::get('app.url') . "<br>";
    echo "Debug Mode: " . (Config::get('app.debug') ? 'On' : 'Off') . "<br>";
} catch (Exception $e) {
    echo "Config error: ❌ " . $e->getMessage() . "<br>";
}
echo "<br>";

// Test 5: Check database connection
echo "<h2>5. Database Connection</h2>";
try {
    require_once 'config/database.php';
    $db = Database::getInstance();
    echo "Database connected: ✅<br>";
    
    // Test a simple query
    $stmt = $db->query("SELECT COUNT(*) FROM users");
    $userCount = $stmt->fetchColumn();
    echo "Users in database: $userCount<br>";
    
    $stmt = $db->query("SELECT COUNT(*) FROM subscription_plans");
    $planCount = $stmt->fetchColumn();
    echo "Subscription plans: $planCount<br>";
    
} catch (Exception $e) {
    echo "Database error: ❌ " . $e->getMessage() . "<br>";
}
echo "<br>";

// Test 6: Check routing
echo "<h2>6. Routing Test</h2>";
try {
    require_once 'includes/router.php';
    echo "Router loaded: ✅<br>";
    
    // Test URL parsing
    $testUrl = '/test';
    echo "Current URL: " . $_SERVER['REQUEST_URI'] . "<br>";
    echo "Script name: " . $_SERVER['SCRIPT_NAME'] . "<br>";
    echo "Base path: " . dirname($_SERVER['SCRIPT_NAME']) . "<br>";
    
} catch (Exception $e) {
    echo "Router error: ❌ " . $e->getMessage() . "<br>";
}
echo "<br>";

// Test 7: Check helper functions
echo "<h2>7. Helper Functions</h2>";
try {
    require_once 'includes/functions.php';
    require_once 'includes/auth.php';
    echo "Helper functions loaded: ✅<br>";
    
    // Test CSRF token generation
    $token = csrf_token();
    echo "CSRF token generated: " . (strlen($token) > 10 ? "✅" : "❌") . "<br>";
    
    // Test asset URL generation
    $assetUrl = asset('public/css/app.css');
    echo "Asset URL: $assetUrl<br>";
    
} catch (Exception $e) {
    echo "Helper functions error: ❌ " . $e->getMessage() . "<br>";
}
echo "<br>";

echo "<h2>Summary</h2>";
echo "<p>If all tests show ✅, your application should be working correctly.</p>";
echo "<p><a href='/'>Go to Application</a></p>";
?>
