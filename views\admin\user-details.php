<?php
ob_start();
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 fw-bold">User Details</h1>
                    <p class="text-muted"><?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?></p>
                </div>
                <div>
                    <a href="<?= url('admin/users') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Users
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- User Information -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">User Information</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="avatar-circle bg-primary text-white mx-auto mb-3" style="width: 80px; height: 80px; font-size: 24px;">
                            <?= strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)) ?>
                        </div>
                        <h5 class="fw-bold"><?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?></h5>
                        <p class="text-muted"><?= htmlspecialchars($user['email']) ?></p>
                        
                        <?php if ($user['status'] === 'active'): ?>
                            <span class="badge bg-success">Active</span>
                        <?php elseif ($user['status'] === 'suspended'): ?>
                            <span class="badge bg-warning">Suspended</span>
                        <?php else: ?>
                            <span class="badge bg-danger"><?= ucfirst($user['status']) ?></span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="border-end">
                                <h6 class="fw-bold text-primary"><?= count($recentImages) ?></h6>
                                <small class="text-muted">Images</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border-end">
                                <h6 class="fw-bold text-primary"><?= $apiKeysCount ?></h6>
                                <small class="text-muted">API Keys</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <h6 class="fw-bold text-primary"><?= $usage['daily'] ?></h6>
                            <small class="text-muted">Today</small>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="mb-3">
                        <strong>Joined:</strong><br>
                        <span class="text-muted"><?= date('M j, Y', strtotime($user['created_at'])) ?></span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Last Login:</strong><br>
                        <span class="text-muted">
                            <?= $user['last_login'] ? time_ago($user['last_login']) : 'Never' ?>
                        </span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Email Verified:</strong><br>
                        <span class="text-muted">
                            <?= $user['email_verified'] ? 'Yes' : 'No' ?>
                        </span>
                    </div>
                    
                    <!-- Actions -->
                    <div class="d-grid gap-2 mt-4">
                        <?php if ($user['status'] === 'active'): ?>
                            <button type="button" class="btn btn-warning" onclick="suspendUser()">
                                <i class="fas fa-ban me-2"></i>Suspend User
                            </button>
                        <?php elseif ($user['status'] === 'suspended'): ?>
                            <button type="button" class="btn btn-success" onclick="activateUser()">
                                <i class="fas fa-check me-2"></i>Activate User
                            </button>
                        <?php endif; ?>
                        
                        <button type="button" class="btn btn-outline-primary" onclick="assignPlan()">
                            <i class="fas fa-credit-card me-2"></i>Assign Plan
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Subscription & Usage -->
        <div class="col-lg-8">
            <!-- Current Subscription -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Current Subscription</h5>
                </div>
                <div class="card-body">
                    <?php if ($subscription): ?>
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h6 class="fw-bold text-primary"><?= htmlspecialchars($subscription['name']) ?></h6>
                                <p class="text-muted mb-2"><?= htmlspecialchars($subscription['description']) ?></p>
                                
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="border-end">
                                            <h6 class="fw-bold"><?= number_format($subscription['daily_generations']) ?></h6>
                                            <small class="text-muted">Daily Limit</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="border-end">
                                            <h6 class="fw-bold"><?= number_format($subscription['monthly_generations']) ?></h6>
                                            <small class="text-muted">Monthly Limit</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <h6 class="fw-bold"><?= $subscription['max_bulk_size'] ?></h6>
                                        <small class="text-muted">Bulk Size</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <div class="mb-2">
                                    <span class="h5 fw-bold">$<?= number_format($subscription['price'], 2) ?></span>
                                    <small class="text-muted">/<?= $subscription['billing_cycle'] ?></small>
                                </div>
                                
                                <?php if ($subscription['status'] === 'active'): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary"><?= ucfirst($subscription['status']) ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-3">
                            <i class="fas fa-credit-card fa-2x text-muted mb-2"></i>
                            <p class="text-muted">No active subscription</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Usage Statistics -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Usage Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Daily Usage</h6>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>Images Generated</span>
                                <span class="fw-bold"><?= $usage['daily'] ?> / <?= $subscription['daily_generations'] ?? 5 ?></span>
                            </div>
                            <div class="progress mb-3" style="height: 8px;">
                                <?php 
                                $dailyPercent = ($subscription['daily_generations'] ?? 5) > 0 
                                    ? ($usage['daily'] / ($subscription['daily_generations'] ?? 5)) * 100 
                                    : 0;
                                ?>
                                <div class="progress-bar" style="width: <?= min($dailyPercent, 100) ?>%"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="fw-bold">Monthly Usage</h6>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>Images Generated</span>
                                <span class="fw-bold"><?= $usage['monthly'] ?> / <?= $subscription['monthly_generations'] ?? 50 ?></span>
                            </div>
                            <div class="progress mb-3" style="height: 8px;">
                                <?php 
                                $monthlyPercent = ($subscription['monthly_generations'] ?? 50) > 0 
                                    ? ($usage['monthly'] / ($subscription['monthly_generations'] ?? 50)) * 100 
                                    : 0;
                                ?>
                                <div class="progress-bar bg-info" style="width: <?= min($monthlyPercent, 100) ?>%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Images -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Recent Images</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($recentImages)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Prompt</th>
                                        <th>Provider</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($recentImages, 0, 10) as $image): ?>
                                        <tr>
                                            <td>
                                                <div style="max-width: 200px;">
                                                    <?= htmlspecialchars(substr($image['prompt'], 0, 50)) ?>
                                                    <?= strlen($image['prompt']) > 50 ? '...' : '' ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?= ucfirst(str_replace('_', ' ', $image['ai_provider'])) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($image['status'] === 'completed'): ?>
                                                    <span class="badge bg-success">Completed</span>
                                                <?php elseif ($image['status'] === 'failed'): ?>
                                                    <span class="badge bg-danger">Failed</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning"><?= ucfirst($image['status']) ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="text-muted"><?= time_ago($image['created_at']) ?></span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-3">
                            <i class="fas fa-images fa-2x text-muted mb-2"></i>
                            <p class="text-muted">No images generated yet</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function suspendUser() {
    if (confirm('Are you sure you want to suspend this user?')) {
        fetch(`<?= url('admin/users/' . $user['id'] . '/suspend') ?>`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': '<?= csrf_token() ?>'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Utils.showToast('User suspended successfully', 'success');
                location.reload();
            } else {
                Utils.showToast(data.message || 'Failed to suspend user', 'danger');
            }
        })
        .catch(error => {
            Utils.showToast('An error occurred', 'danger');
            console.error('Suspend error:', error);
        });
    }
}

function activateUser() {
    if (confirm('Are you sure you want to activate this user?')) {
        fetch(`<?= url('admin/users/' . $user['id'] . '/activate') ?>`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': '<?= csrf_token() ?>'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Utils.showToast('User activated successfully', 'success');
                location.reload();
            } else {
                Utils.showToast(data.message || 'Failed to activate user', 'danger');
            }
        })
        .catch(error => {
            Utils.showToast('An error occurred', 'danger');
            console.error('Activate error:', error);
        });
    }
}

function assignPlan() {
    // This would open a modal to assign a plan
    Utils.showToast('Plan assignment feature coming soon', 'info');
}
</script>

<style>
.avatar-circle {
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}
</style>

<?php
$content = ob_get_clean();
$title = 'User Details';
include __DIR__ . '/../layout/app.php';
?>
