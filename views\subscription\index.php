<?php
ob_start();
?>

<div class="container py-4">
    <div class="row">
        <div class="col-md-3">
            <!-- Settings Navigation -->
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h6 class="fw-bold mb-3">Settings</h6>
                    <nav class="nav nav-pills flex-column">
                        <a class="nav-link" href="<?= url('settings') ?>">
                            <i class="fas fa-user me-2"></i>Profile
                        </a>
                        <a class="nav-link" href="<?= url('api-keys') ?>">
                            <i class="fas fa-key me-2"></i>API Keys
                        </a>
                        <a class="nav-link active" href="<?= url('subscription') ?>">
                            <i class="fas fa-credit-card me-2"></i>Subscription
                        </a>
                    </nav>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <!-- Current Subscription -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Current Subscription</h5>
                </div>
                <div class="card-body">
                    <?php if ($subscription): ?>
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h4 class="fw-bold text-primary mb-1"><?= htmlspecialchars($subscription['name']) ?></h4>
                                <p class="text-muted mb-2"><?= htmlspecialchars($subscription['description']) ?></p>
                                
                                <div class="row text-center mt-3">
                                    <div class="col-4">
                                        <div class="border-end">
                                            <h6 class="fw-bold text-primary"><?= number_format($subscription['daily_generations']) ?></h6>
                                            <small class="text-muted">Daily Limit</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="border-end">
                                            <h6 class="fw-bold text-primary"><?= number_format($subscription['monthly_generations']) ?></h6>
                                            <small class="text-muted">Monthly Limit</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <h6 class="fw-bold text-primary"><?= $subscription['max_bulk_size'] ?></h6>
                                        <small class="text-muted">Bulk Size</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <div class="mb-2">
                                    <span class="h4 fw-bold">$<?= number_format($subscription['price'], 2) ?></span>
                                    <small class="text-muted">/<?= $subscription['billing_cycle'] ?></small>
                                </div>
                                
                                <?php if ($subscription['status'] === 'active'): ?>
                                    <span class="badge bg-success mb-2">Active</span>
                                <?php elseif ($subscription['status'] === 'canceled'): ?>
                                    <span class="badge bg-warning mb-2">Canceled</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary mb-2"><?= ucfirst($subscription['status']) ?></span>
                                <?php endif; ?>
                                
                                <?php if ($subscription['current_period_end']): ?>
                                    <div class="small text-muted">
                                        <?php if ($subscription['cancel_at_period_end']): ?>
                                            Expires: <?= date('M j, Y', strtotime($subscription['current_period_end'])) ?>
                                        <?php else: ?>
                                            Renews: <?= date('M j, Y', strtotime($subscription['current_period_end'])) ?>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Subscription Actions -->
                        <div class="mt-4 pt-3 border-top">
                            <div class="d-flex gap-2">
                                <?php if ($subscription['status'] === 'active' && !$subscription['cancel_at_period_end']): ?>
                                    <button type="button" class="btn btn-outline-danger" onclick="cancelSubscription()">
                                        <i class="fas fa-times me-2"></i>Cancel Subscription
                                    </button>
                                <?php elseif ($subscription['cancel_at_period_end']): ?>
                                    <button type="button" class="btn btn-success" onclick="resumeSubscription()">
                                        <i class="fas fa-play me-2"></i>Resume Subscription
                                    </button>
                                <?php endif; ?>
                                
                                <?php if ($subscription['price'] > 0): ?>
                                    <a href="#plans" class="btn btn-primary">
                                        <i class="fas fa-arrow-up me-2"></i>Upgrade Plan
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No Active Subscription</h6>
                            <p class="text-muted">You're currently on the free plan. Upgrade to unlock more features.</p>
                            <a href="#plans" class="btn btn-primary">
                                <i class="fas fa-arrow-up me-2"></i>Choose a Plan
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Usage Statistics -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Usage Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Daily Usage</h6>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>Images Generated</span>
                                <span class="fw-bold"><?= $usage['daily'] ?> / <?= $subscription['daily_generations'] ?? 5 ?></span>
                            </div>
                            <div class="progress mb-3" style="height: 8px;">
                                <?php 
                                $dailyPercent = ($subscription['daily_generations'] ?? 5) > 0 
                                    ? ($usage['daily'] / ($subscription['daily_generations'] ?? 5)) * 100 
                                    : 0;
                                ?>
                                <div class="progress-bar" style="width: <?= min($dailyPercent, 100) ?>%"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="fw-bold">Monthly Usage</h6>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>Images Generated</span>
                                <span class="fw-bold"><?= $usage['monthly'] ?> / <?= $subscription['monthly_generations'] ?? 50 ?></span>
                            </div>
                            <div class="progress mb-3" style="height: 8px;">
                                <?php 
                                $monthlyPercent = ($subscription['monthly_generations'] ?? 50) > 0 
                                    ? ($usage['monthly'] / ($subscription['monthly_generations'] ?? 50)) * 100 
                                    : 0;
                                ?>
                                <div class="progress-bar bg-info" style="width: <?= min($monthlyPercent, 100) ?>%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Available Plans -->
            <div id="plans" class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Available Plans</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <?php foreach ($plans as $plan): ?>
                            <div class="col-md-4">
                                <div class="card h-100 border <?= $plan['name'] === 'Pro' ? 'border-primary' : '' ?>">
                                    <?php if ($plan['name'] === 'Pro'): ?>
                                        <div class="card-header bg-primary text-white text-center py-2">
                                            <small class="fw-bold">MOST POPULAR</small>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="card-body text-center p-3">
                                        <h6 class="fw-bold"><?= htmlspecialchars($plan['name']) ?></h6>
                                        <div class="mb-2">
                                            <span class="h4 fw-bold">$<?= number_format($plan['price'], 0) ?></span>
                                            <small class="text-muted">/month</small>
                                        </div>
                                        
                                        <ul class="list-unstyled small mb-3">
                                            <li class="mb-1">
                                                <i class="fas fa-check text-success me-1"></i>
                                                <?= number_format($plan['daily_generations']) ?> images/day
                                            </li>
                                            <li class="mb-1">
                                                <i class="fas fa-check text-success me-1"></i>
                                                <?= number_format($plan['monthly_generations']) ?> images/month
                                            </li>
                                            <li class="mb-1">
                                                <i class="fas fa-check text-success me-1"></i>
                                                Up to <?= $plan['max_bulk_size'] ?> bulk generations
                                            </li>
                                        </ul>
                                        
                                        <?php if ($subscription && $subscription['plan_id'] == $plan['id']): ?>
                                            <button class="btn btn-outline-secondary w-100" disabled>
                                                Current Plan
                                            </button>
                                        <?php else: ?>
                                            <button type="button" 
                                                    class="btn <?= $plan['name'] === 'Pro' ? 'btn-primary' : 'btn-outline-primary' ?> w-100"
                                                    onclick="upgradeToPlan(<?= $plan['id'] ?>, '<?= htmlspecialchars($plan['name']) ?>')">
                                                <?= $plan['price'] == 0 ? 'Downgrade' : 'Upgrade' ?>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function cancelSubscription() {
    if (confirm('Are you sure you want to cancel your subscription? You will still have access until the end of your billing period.')) {
        fetch('<?= url('subscription/cancel') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': '<?= csrf_token() ?>'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Utils.showToast('Subscription canceled successfully', 'success');
                location.reload();
            } else {
                Utils.showToast(data.message || 'Failed to cancel subscription', 'danger');
            }
        })
        .catch(error => {
            Utils.showToast('An error occurred', 'danger');
            console.error('Cancel error:', error);
        });
    }
}

function resumeSubscription() {
    fetch('<?= url('subscription/resume') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': '<?= csrf_token() ?>'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Utils.showToast('Subscription resumed successfully', 'success');
            location.reload();
        } else {
            Utils.showToast(data.message || 'Failed to resume subscription', 'danger');
        }
    })
    .catch(error => {
        Utils.showToast('An error occurred', 'danger');
        console.error('Resume error:', error);
    });
}

function upgradeToPlan(planId, planName) {
    if (confirm(`Are you sure you want to upgrade to the ${planName} plan?`)) {
        fetch('<?= url('subscription/upgrade') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': '<?= csrf_token() ?>'
            },
            body: JSON.stringify({ plan_id: planId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.checkout_url) {
                    window.location.href = data.checkout_url;
                } else {
                    Utils.showToast('Plan updated successfully', 'success');
                    location.reload();
                }
            } else {
                Utils.showToast(data.message || 'Failed to upgrade plan', 'danger');
            }
        })
        .catch(error => {
            Utils.showToast('An error occurred', 'danger');
            console.error('Upgrade error:', error);
        });
    }
}
</script>

<?php
$content = ob_get_clean();
$title = 'Subscription';
include __DIR__ . '/../layout/app.php';
?>
