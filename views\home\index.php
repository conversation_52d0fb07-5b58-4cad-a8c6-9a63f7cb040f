<?php
ob_start();
?>

<!-- Hero Section -->
<section class="hero-section bg-gradient-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center min-vh-75">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    Generate Stunning AI Images in Seconds
                </h1>
                <p class="lead mb-4">
                    Transform your ideas into beautiful images using cutting-edge AI technology.
                    Bring your own API keys and start creating amazing visuals today.
                </p>
                <div class="d-flex gap-3 mb-4">
                    <a href="<?= url('register') ?>" class="btn btn-light btn-lg px-4">
                        <i class="fas fa-rocket me-2"></i>Get Started Free
                    </a>
                    <a href="#features" class="btn btn-outline-light btn-lg px-4">
                        <i class="fas fa-play me-2"></i>Learn More
                    </a>
                </div>
                <div class="row text-center mt-5">
                    <div class="col-4">
                        <h3 class="fw-bold"><?= number_format($totalImages) ?>+</h3>
                        <small>Images Generated</small>
                    </div>
                    <div class="col-4">
                        <h3 class="fw-bold"><?= number_format($totalUsers) ?>+</h3>
                        <small>Happy Users</small>
                    </div>
                    <div class="col-4">
                        <h3 class="fw-bold">4+</h3>
                        <small>AI Providers</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-image-grid">
                    <?php if (!empty($sampleImages)): ?>
                        <div class="row g-2">
                            <?php foreach (array_slice($sampleImages, 0, 4) as $image): ?>
                                <div class="col-6">
                                    <div class="card bg-white shadow-sm">
                                        <img src="<?= htmlspecialchars($image['file_path']) ?>"
                                             class="card-img-top"
                                             alt="<?= htmlspecialchars($image['prompt']) ?>"
                                             style="height: 150px; object-fit: cover;">
                                        <div class="card-body p-2">
                                            <small class="text-muted">
                                                <?= htmlspecialchars(substr($image['prompt'], 0, 50)) ?>...
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center">
                            <i class="fas fa-image fa-5x opacity-50"></i>
                            <p class="mt-3">Sample images will appear here</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section id="features" class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold">Powerful Features</h2>
            <p class="lead text-muted">Everything you need to create amazing AI images</p>
        </div>

        <div class="row g-4">
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="fas fa-key fa-lg"></i>
                        </div>
                        <h5 class="fw-bold">Bring Your Own API</h5>
                        <p class="text-muted">Use your own API keys from Together AI, Runware, Replicate, and FAL AI. Full control over your costs and usage.</p>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="fas fa-layer-group fa-lg"></i>
                        </div>
                        <h5 class="fw-bold">Bulk Generation</h5>
                        <p class="text-muted">Generate multiple images at once with our bulk processing feature. Upload prompts via CSV or text file.</p>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="fas fa-chart-line fa-lg"></i>
                        </div>
                        <h5 class="fw-bold">Usage Analytics</h5>
                        <p class="text-muted">Track your usage, monitor costs, and analyze your image generation patterns with detailed analytics.</p>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="fas fa-palette fa-lg"></i>
                        </div>
                        <h5 class="fw-bold">Multiple Styles</h5>
                        <p class="text-muted">Choose from various artistic styles and presets to match your creative vision perfectly.</p>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon bg-danger text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="fas fa-expand-arrows-alt fa-lg"></i>
                        </div>
                        <h5 class="fw-bold">Custom Dimensions</h5>
                        <p class="text-muted">Generate images in various sizes and aspect ratios to fit your specific needs and use cases.</p>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon bg-secondary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <i class="fas fa-shield-alt fa-lg"></i>
                        </div>
                        <h5 class="fw-bold">Secure & Private</h5>
                        <p class="text-muted">Your API keys and generated images are encrypted and stored securely. Your privacy is our priority.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Pricing Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold">Simple, Transparent Pricing</h2>
            <p class="lead text-muted">Choose the plan that fits your needs</p>
        </div>

        <div class="row g-4 justify-content-center">
            <?php foreach ($plans as $plan): ?>
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-sm <?= $plan['name'] === 'Pro' ? 'border-primary' : '' ?>">
                        <?php if ($plan['name'] === 'Pro'): ?>
                            <div class="card-header bg-primary text-white text-center py-2">
                                <small class="fw-bold">MOST POPULAR</small>
                            </div>
                        <?php endif; ?>

                        <div class="card-body text-center p-4">
                            <h5 class="fw-bold"><?= htmlspecialchars($plan['name']) ?></h5>
                            <div class="mb-3">
                                <span class="display-6 fw-bold">$<?= number_format($plan['price'], 0) ?></span>
                                <small class="text-muted">/month</small>
                            </div>
                            <p class="text-muted"><?= htmlspecialchars($plan['description']) ?></p>

                            <ul class="list-unstyled mb-4">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <?= number_format($plan['daily_generations']) ?> images/day
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <?= number_format($plan['monthly_generations']) ?> images/month
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Up to <?= $plan['max_bulk_size'] ?> bulk generations
                                </li>
                                <?php
                                $features = json_decode($plan['features'], true);
                                if ($features):
                                    foreach ($features as $feature):
                                ?>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        <?= htmlspecialchars($feature) ?>
                                    </li>
                                <?php
                                    endforeach;
                                endif;
                                ?>
                            </ul>

                            <a href="<?= url('register') ?>" class="btn <?= $plan['name'] === 'Pro' ? 'btn-primary' : 'btn-outline-primary' ?> w-100">
                                <?= $plan['price'] == 0 ? 'Get Started Free' : 'Choose Plan' ?>
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white">
    <div class="container text-center">
        <h2 class="display-5 fw-bold mb-4">Ready to Start Creating?</h2>
        <p class="lead mb-4">Join thousands of creators using AI to bring their ideas to life</p>
        <a href="<?= url('register') ?>" class="btn btn-light btn-lg px-5">
            <i class="fas fa-rocket me-2"></i>Start Free Today
        </a>
    </div>
</section>

<?php
$content = ob_get_clean();
$title = 'Home';
$bodyClass = 'landing-page';
include __DIR__ . '/../layout/app.php';
?>
