<?php
ob_start();
?>

<div class="container-fluid py-4">
    <!-- <PERSON><PERSON>er -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 fw-bold">Admin Dashboard</h1>
            <p class="text-muted">System overview and management</p>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="row g-4 mb-4">
        <!-- Total Users -->
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Total Users</h6>
                            <h4 class="mb-0"><?= number_format($metrics['total_users']) ?></h4>
                            <small class="text-success">
                                <i class="fas fa-user-check me-1"></i>
                                <?= number_format($metrics['active_users']) ?> active
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Images -->
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-images"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Total Images</h6>
                            <h4 class="mb-0"><?= number_format($metrics['total_images']) ?></h4>
                            <small class="text-info">
                                <i class="fas fa-calendar-day me-1"></i>
                                <?= number_format($metrics['images_today']) ?> today
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Revenue -->
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Monthly Revenue</h6>
                            <h4 class="mb-0">$<?= number_format($metrics['monthly_revenue'], 2) ?></h4>
                            <small class="text-success">
                                <i class="fas fa-credit-card me-1"></i>
                                <?= number_format($metrics['active_subscriptions']) ?> subscriptions
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-server"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">System Status</h6>
                            <h4 class="mb-0 text-success">Online</h4>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                <?= date('M j, Y H:i') ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- Recent Users -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Users</h5>
                    <a href="<?= url('admin/users') ?>" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($recentUsers)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>User</th>
                                        <th>Email</th>
                                        <th>Status</th>
                                        <th>Joined</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentUsers as $user): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px; font-size: 12px;">
                                                        <?= strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)) ?>
                                                    </div>
                                                    <div>
                                                        <div class="fw-medium"><?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?= htmlspecialchars($user['email']) ?></td>
                                            <td>
                                                <span class="badge bg-<?= $user['status'] === 'active' ? 'success' : ($user['status'] === 'suspended' ? 'danger' : 'warning') ?>">
                                                    <?= ucfirst($user['status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted"><?= date('M j, Y', strtotime($user['created_at'])) ?></small>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users text-muted mb-2" style="font-size: 2rem;"></i>
                            <p class="text-muted">No users found</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Images -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Images</h5>
                    <a href="<?= url('admin/analytics') ?>" class="btn btn-sm btn-outline-primary">View Analytics</a>
                </div>
                <div class="card-body">
                    <?php if (!empty($recentImages)): ?>
                        <div class="row g-2">
                            <?php foreach (array_slice($recentImages, 0, 8) as $image): ?>
                                <div class="col-3">
                                    <div class="card border-0 shadow-sm">
                                        <img src="<?= htmlspecialchars($image['file_path']) ?>"
                                             class="card-img-top"
                                             alt="Generated image"
                                             style="height: 80px; object-fit: cover;">
                                        <div class="card-body p-1">
                                            <small class="text-muted d-block text-truncate">
                                                <?= htmlspecialchars($image['first_name'] . ' ' . $image['last_name']) ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-images text-muted mb-2" style="font-size: 2rem;"></i>
                            <p class="text-muted">No images generated yet</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="<?= url('admin/users') ?>" class="btn btn-outline-primary w-100">
                                <i class="fas fa-users me-2"></i>Manage Users
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?= url('admin/plans') ?>" class="btn btn-outline-success w-100">
                                <i class="fas fa-credit-card me-2"></i>Subscription Plans
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?= url('admin/analytics') ?>" class="btn btn-outline-info w-100">
                                <i class="fas fa-chart-bar me-2"></i>Analytics
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?= url('admin/settings') ?>" class="btn btn-outline-warning w-100">
                                <i class="fas fa-cog me-2"></i>System Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
$title = 'Admin Dashboard';
include __DIR__ . '/../layout/app.php';
?>
