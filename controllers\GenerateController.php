<?php

class GenerateController extends BaseController {

    public function index() {
        Auth::requireAuth();

        $userId = Auth::id();
        $subscription = Auth::getUserSubscription($userId);
        $usage = Auth::getUserUsage($userId);

        // Check if user has API keys configured
        $db = Database::getInstance();
        $stmt = $db->prepare("SELECT provider, is_active FROM user_ai_provider_keys WHERE user_id = ? AND is_active = 1");
        $stmt->execute([$userId]);
        $apiKeys = $stmt->fetchAll();

        // Get available providers
        $supportedProviders = get_supported_ai_providers();

        $this->view('generate/index', [
            'subscription' => $subscription,
            'usage' => $usage,
            'apiKeys' => $apiKeys,
            'supportedProviders' => $supportedProviders,
            'canGenerate' => Auth::canGenerateImage($userId)
        ]);
    }

    public function single() {
        Auth::requireAuth();
        $this->validateCsrf();

        $userId = Auth::id();

        // Check if user can generate images
        if (!Auth::canGenerateImage($userId)) {
            flash('error', 'You have reached your generation limit for today.');
            $this->redirect('/generate');
        }

        // Validate input
        $data = [
            'prompt' => sanitize($_POST['prompt'] ?? ''),
            'negative_prompt' => sanitize($_POST['negative_prompt'] ?? ''),
            'width' => (int)($_POST['width'] ?? 512),
            'height' => (int)($_POST['height'] ?? 512),
            'style' => sanitize($_POST['style'] ?? ''),
            'provider' => sanitize($_POST['provider'] ?? ''),
            'model' => sanitize($_POST['model'] ?? ''),
        ];

        $errors = $this->validate($data, [
            'prompt' => 'required|min:3|max:1000',
            'width' => 'required',
            'height' => 'required',
            'provider' => 'required',
            'model' => 'required'
        ]);

        if (!empty($errors)) {
            foreach ($errors as $field => $fieldErrors) {
                foreach ($fieldErrors as $error) {
                    flash('error', $error);
                }
            }
            $this->redirect('/generate');
        }

        // Validate dimensions
        if (!is_valid_image_dimensions($data['width'], $data['height'])) {
            flash('error', 'Invalid image dimensions selected.');
            $this->redirect('/generate');
        }

        // Check if user has API key for selected provider
        $db = Database::getInstance();
        $stmt = $db->prepare("SELECT api_key FROM user_ai_provider_keys WHERE user_id = ? AND provider = ? AND is_active = 1");
        $stmt->execute([$userId, $data['provider']]);
        $apiKeyData = $stmt->fetch();

        if (!$apiKeyData) {
            flash('error', 'No API key configured for selected provider.');
            $this->redirect('/generate');
        }

        try {
            // Create image generation record
            $stmt = $db->prepare("
                INSERT INTO generated_images (user_id, prompt, negative_prompt, width, height, style, ai_provider, model, file_path, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')
            ");

            $filename = 'img_' . time() . '_' . uniqid() . '.png';
            $filePath = 'storage/generated/' . $filename;

            $stmt->execute([
                $userId,
                $data['prompt'],
                $data['negative_prompt'],
                $data['width'],
                $data['height'],
                $data['style'],
                $data['provider'],
                $data['model'],
                $filePath
            ]);

            $imageId = $db->lastInsertId();

            // Start image generation process
            $this->generateImage($imageId, $data, decrypt($apiKeyData['api_key']));

            // Record usage
            Auth::recordUsage($userId, 'image_generation');

            flash('success', 'Image generation started! Please wait...');
            $this->redirect('/generate?image_id=' . $imageId);

        } catch (Exception $e) {
            log_error('Image generation failed: ' . $e->getMessage(), $data);
            flash('error', 'Failed to start image generation. Please try again.');
            $this->redirect('/generate');
        }
    }

    public function bulk() {
        Auth::requireAuth();

        $userId = Auth::id();
        $subscription = Auth::getUserSubscription($userId);

        // Check if user has API keys configured
        $db = Database::getInstance();
        $stmt = $db->prepare("SELECT provider, is_active FROM user_ai_provider_keys WHERE user_id = ? AND is_active = 1");
        $stmt->execute([$userId]);
        $apiKeys = $stmt->fetchAll();

        $this->view('generate/bulk', [
            'subscription' => $subscription,
            'apiKeys' => $apiKeys,
            'maxBulkSize' => $subscription['max_bulk_size'] ?? 5
        ]);
    }

    public function processBulk() {
        Auth::requireAuth();
        $this->validateCsrf();

        $userId = Auth::id();
        $subscription = Auth::getUserSubscription($userId);
        $maxBulkSize = $subscription['max_bulk_size'] ?? 5;

        // Handle file upload or text input
        $prompts = [];

        if (!empty($_FILES['prompts_file']['tmp_name'])) {
            $file = $_FILES['prompts_file'];
            $fileContent = file_get_contents($file['tmp_name']);

            if (pathinfo($file['name'], PATHINFO_EXTENSION) === 'csv') {
                $lines = str_getcsv($fileContent, "\n");
                foreach ($lines as $line) {
                    $data = str_getcsv($line);
                    if (!empty($data[0])) {
                        $prompts[] = trim($data[0]);
                    }
                }
            } else {
                $prompts = array_filter(array_map('trim', explode("\n", $fileContent)));
            }
        } elseif (!empty($_POST['prompts_text'])) {
            $prompts = array_filter(array_map('trim', explode("\n", $_POST['prompts_text'])));
        }

        if (empty($prompts)) {
            flash('error', 'No prompts provided.');
            $this->redirect('/bulk-generate');
        }

        if (count($prompts) > $maxBulkSize) {
            flash('error', "Maximum {$maxBulkSize} prompts allowed for your plan.");
            $this->redirect('/bulk-generate');
        }

        // Check if user can generate this many images
        $usage = Auth::getUserUsage($userId);
        $remainingDaily = $subscription['daily_generations'] - $usage['daily'];
        $remainingMonthly = $subscription['monthly_generations'] - $usage['monthly'];

        if (count($prompts) > $remainingDaily || count($prompts) > $remainingMonthly) {
            flash('error', 'Not enough remaining generations for this bulk request.');
            $this->redirect('/bulk-generate');
        }

        $data = [
            'width' => (int)($_POST['width'] ?? 512),
            'height' => (int)($_POST['height'] ?? 512),
            'style' => sanitize($_POST['style'] ?? ''),
            'provider' => sanitize($_POST['provider'] ?? ''),
            'model' => sanitize($_POST['model'] ?? ''),
        ];

        try {
            $db = Database::getInstance();
            $batchId = 'batch_' . time() . '_' . uniqid();

            // Get API key
            $stmt = $db->prepare("SELECT api_key FROM user_ai_provider_keys WHERE user_id = ? AND provider = ? AND is_active = 1");
            $stmt->execute([$userId, $data['provider']]);
            $apiKeyData = $stmt->fetch();

            if (!$apiKeyData) {
                flash('error', 'No API key configured for selected provider.');
                $this->redirect('/bulk-generate');
            }

            $db->beginTransaction();

            // Create records for all images
            foreach ($prompts as $prompt) {
                $filename = 'bulk_' . time() . '_' . uniqid() . '.png';
                $filePath = 'storage/generated/' . $filename;

                $stmt = $db->prepare("
                    INSERT INTO generated_images (user_id, prompt, width, height, style, ai_provider, model, file_path, batch_id, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')
                ");

                $stmt->execute([
                    $userId,
                    $prompt,
                    $data['width'],
                    $data['height'],
                    $data['style'],
                    $data['provider'],
                    $data['model'],
                    $filePath,
                    $batchId
                ]);

                // Record usage for each image
                Auth::recordUsage($userId, 'bulk_generation');
            }

            $db->commit();

            // Start bulk generation process (in background)
            $this->processBulkGeneration($batchId, $data, decrypt($apiKeyData['api_key']));

            flash('success', 'Bulk generation started! Check your gallery for progress.');
            $this->redirect('/gallery?batch=' . $batchId);

        } catch (Exception $e) {
            $db->rollback();
            log_error('Bulk generation failed: ' . $e->getMessage(), $data);
            flash('error', 'Failed to start bulk generation. Please try again.');
            $this->redirect('/bulk-generate');
        }
    }

    private function generateImage($imageId, $data, $apiKey) {
        // This would typically be handled by a background job queue
        // For now, we'll simulate the process

        $db = Database::getInstance();

        try {
            // Update status to generating
            $stmt = $db->prepare("UPDATE generated_images SET status = 'generating' WHERE id = ?");
            $stmt->execute([$imageId]);

            // Call AI provider API
            $result = $this->callAIProvider($data['provider'], $apiKey, $data);

            if ($result['success']) {
                // Download and save image
                $imageData = file_get_contents($result['image_url']);
                $stmt = $db->prepare("SELECT file_path FROM generated_images WHERE id = ?");
                $stmt->execute([$imageId]);
                $filePath = $stmt->fetchColumn();

                file_put_contents($filePath, $imageData);

                // Update record
                $stmt = $db->prepare("
                    UPDATE generated_images
                    SET status = 'completed', file_size = ?, generation_time = ?
                    WHERE id = ?
                ");
                $stmt->execute([strlen($imageData), $result['generation_time'], $imageId]);

            } else {
                // Update with error
                $stmt = $db->prepare("UPDATE generated_images SET status = 'failed', error_message = ? WHERE id = ?");
                $stmt->execute([$result['error'], $imageId]);
            }

        } catch (Exception $e) {
            $stmt = $db->prepare("UPDATE generated_images SET status = 'failed', error_message = ? WHERE id = ?");
            $stmt->execute([$e->getMessage(), $imageId]);
        }
    }

    private function processBulkGeneration($batchId, $data, $apiKey) {
        // This would typically be handled by a background job queue
        // For demonstration, we'll process a few images

        $db = Database::getInstance();
        $stmt = $db->prepare("SELECT id FROM generated_images WHERE batch_id = ? AND status = 'pending' LIMIT 3");
        $stmt->execute([$batchId]);
        $images = $stmt->fetchAll();

        foreach ($images as $image) {
            $this->generateImage($image['id'], $data, $apiKey);
        }
    }

    private function callAIProvider($provider, $apiKey, $data) {
        // Simulate API call - in real implementation, this would call actual AI APIs
        sleep(2); // Simulate processing time

        return [
            'success' => true,
            'image_url' => 'https://picsum.photos/' . $data['width'] . '/' . $data['height'],
            'generation_time' => rand(1, 5)
        ];
    }
}
