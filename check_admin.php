<?php
/**
 * Check Admin User Script
 * This script checks if an admin user exists and can create a default one if needed
 */

require_once 'config/app.php';
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Load configuration
Config::load();

try {
    $db = Database::getInstance();
    
    echo "🔍 Checking for admin users...\n\n";
    
    // Check if admin user exists
    $stmt = $db->prepare("SELECT id, email, first_name, last_name, created_at FROM users WHERE role = 'admin'");
    $stmt->execute();
    $admins = $stmt->fetchAll();
    
    if (empty($admins)) {
        echo "❌ No admin users found!\n\n";
        echo "Creating default admin user...\n";
        
        // Create default admin user
        $defaultEmail = '<EMAIL>';
        $defaultPassword = 'admin123';
        $defaultFirstName = 'Admin';
        $defaultLastName = 'User';
        
        $stmt = $db->prepare("
            INSERT INTO users (email, password_hash, first_name, last_name, role, email_verified, status) 
            VALUES (?, ?, ?, ?, 'admin', 1, 'active')
        ");
        
        $passwordHash = password_hash($defaultPassword, PASSWORD_ARGON2ID);
        
        if ($stmt->execute([$defaultEmail, $passwordHash, $defaultFirstName, $defaultLastName])) {
            $adminId = $db->lastInsertId();
            
            // Assign business plan to admin
            $stmt = $db->prepare("
                INSERT INTO user_subscriptions (user_id, plan_id, status, current_period_start, current_period_end) 
                VALUES (?, 3, 'active', CURRENT_TIMESTAMP, DATE('now', '+1 year'))
            ");
            $stmt->execute([$adminId]);
            
            echo "✅ Default admin user created successfully!\n\n";
            echo "📧 Admin Credentials:\n";
            echo "   Email: $defaultEmail\n";
            echo "   Password: $defaultPassword\n\n";
            echo "⚠️  IMPORTANT: Please change these credentials after first login!\n\n";
        } else {
            echo "❌ Failed to create default admin user\n";
        }
    } else {
        echo "✅ Found " . count($admins) . " admin user(s):\n\n";
        
        foreach ($admins as $admin) {
            echo "👤 Admin User:\n";
            echo "   ID: {$admin['id']}\n";
            echo "   Email: {$admin['email']}\n";
            echo "   Name: {$admin['first_name']} {$admin['last_name']}\n";
            echo "   Created: {$admin['created_at']}\n\n";
        }
        
        echo "ℹ️  If you forgot the password, you can reset it through the forgot password feature\n";
        echo "   or manually update it in the database.\n\n";
    }
    
    echo "🌐 Admin Panel Access:\n";
    echo "   URL: " . Config::get('app.url') . "/admin\n";
    echo "   Login URL: " . Config::get('app.url') . "/login\n\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
